import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import ApiUrl from '../../components/helper-functions/ApiUrl';

export const fetchVendors = createAsyncThunk('vendors/fetchVendors', async () => {
  const response = await axios.get(`${ApiUrl}/vendors`);
  return response.data;
});

export const fetchVendorStatistics = createAsyncThunk(
  'vendors/fetchVendorStatistics',
  async (vendorId, { rejectWithValue }) => {
    try {
      // Get the authentication token from localStorage
      const authData = localStorage.getItem('authData');
      if (!authData) {
        return rejectWithValue('Authentication required. Please log in.');
      }

      const { token } = JSON.parse(authData);
      if (!token) {
        return rejectWithValue('Authentication token not found. Please log in again.');
      }

      // Create a new instance of axios with the token in the headers
      const response = await axios.get(
        `${ApiUrl}/vendors/${vendorId}/statistics`,
        {
          headers: {
            Authorization: token.startsWith('Bearer') ? token : `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        },
      );

      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  },
);

const vendorsSlice = createSlice({
  name: 'vendors',
  initialState: {
    vendors: [],
    statistics: null,
    status: 'idle',
    statisticsStatus: 'idle',
    loading: false,
    error: null,
    statisticsError: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchVendors.pending, (state) => ({
        ...state,
        status: 'loading',
      }))
      .addCase(fetchVendors.fulfilled, (state, action) => ({
        ...state,
        status: 'succeeded',
        vendors: action.payload,
      }))
      .addCase(fetchVendors.rejected, (state, action) => ({
        ...state,
        status: 'failed',
        error: action.error.message,
      }))
      .addCase(fetchVendorStatistics.pending, (state) => ({
        ...state,
        statisticsStatus: 'loading',
      }))
      .addCase(fetchVendorStatistics.fulfilled, (state, action) => ({
        ...state,
        statisticsStatus: 'succeeded',
        statistics: action.payload,
      }))
      .addCase(fetchVendorStatistics.rejected, (state, action) => ({
        ...state,
        statisticsStatus: 'failed',
        statisticsError: action.payload,
      }));
  },
});

export default vendorsSlice.reducer;

export const selectVendors = (state) => state.vendors.vendors;
export const selectVendorsStatus = (state) => state.vendors.status;
export const selectVendorsError = (state) => state.vendors.error;
export const selectVendorStatistics = (state) => state.vendors.statistics;
export const selectVendorStatisticsStatus = (state) => state.vendors.statisticsStatus;
export const selectVendorStatisticsError = (state) => state.vendors.statisticsError;
