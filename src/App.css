*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body,
#root {
  width: 100%;
  height: 100vh;
  background-color: #f5f5f7;
  color: #333;
  font-family: Open Sans, Roboto, Arial, Helvetica, sans-serif, SimSun;
}

ul {
  list-style: none;
}

button {
  border: none;
}

.auth-page {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 0 7rem 0;
  background-color: #f8f9fa;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="%23f8f9fa"/><path d="M0 0L100 100M100 0L0 100" stroke="%23f0f0f0" stroke-width="2"/></svg>');
  background-size: 20px 20px;
  position: relative;
  overflow: hidden;
}

.auth-page::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(248, 180, 0, 0.05) 0%, rgba(248, 180, 0, 0) 70%);
  z-index: 0;
  animation: rotate 60s linear infinite;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.auth-form {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 500px;
  max-height: 100vh;
  overflow-y: auto;
  padding: 0;
  background-color: white;
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.1);
  position: relative;
  color: #333;
  scrollbar-width: thin;
  scrollbar-color: #f8b400 #f0f0f0;
  z-index: 1;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* Login Button Styles */
.login-button {
  width: 100%;
  margin-top: 1.5rem;
  padding: 0.875rem !important;
  font-size: 1.1rem !important;
  transition: all 0.3s ease;
  background-image: linear-gradient(135deg, #f8b400, #e5a700);
  border-radius: 0.5rem;
  position: relative;
  overflow: hidden;
  border: none;
  color: white;
  font-weight: 600;
  cursor: pointer;
}

.login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.6s ease;
}

.login-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 0.5rem 1.5rem rgba(248, 180, 0, 0.4);
}

.login-button:hover::before {
  left: 100%;
}

.auth-form::-webkit-scrollbar {
  width: 8px;
}

.auth-form::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 10px;
}

.auth-form::-webkit-scrollbar-thumb {
  background-color: #f8b400;
  border-radius: 10px;
}

.auth-form-header {
  background-color: #f8b400;
  padding: 1.5rem;
  text-align: center;
  position: sticky;
  top: 0;
  z-index: 10;
  background-image: linear-gradient(135deg, #f8b400, #e5a700);
  box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.1);
}

.auth-form-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none"/><path d="M0 50 Q 25 25, 50 50 T 100 50" stroke="rgba(255,255,255,0.1)" stroke-width="2" fill="none"/></svg>');
  background-size: 50px 50px;
  opacity: 0.5;
}

.role-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 1rem;
  font-size: 0.85rem;
  font-weight: 600;
  margin-top: 0.5rem;
  text-transform: capitalize;
}

.auth-form h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.auth-page .message {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
  margin-top: 0.5rem;
  line-height: 1.4;
}

.auth-form-content {
  padding: 0.5rem;
}

.form-steps {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

.form-label {
  display: block;
  font-size: 0.9rem;
  font-weight: 600;
  color: #444;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.required {
  color: #e74c3c;
  margin-left: 0.25rem;
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  font-size: 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 0.5rem;
  background-color: #fff;
  color: #333;
  transition: all 0.2s ease;
  border-bottom: 0.125rem solid #f8b400;
}

.form-input:focus {
  border-color: #f8b400;
  box-shadow: 0 0 0 2px rgba(248, 180, 0, 0.1);
  outline: none;
  background-color: #fff;
  transform: translateY(-2px);
}

.form-input::placeholder {
  color: #aaa;
}

.input-container {
  position: relative;
}

/* Password Field */
.password-container {
  position: relative;
  width: 100%;
}

.password-toggle {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: #aaa;
  font-size: 1rem;
  padding: 0.25rem;
  transition: color 0.2s ease;
  z-index: 2;
}

.password-toggle:hover {
  color: #f8b400;
}

.password-toggle:focus {
  outline: none;
}

/* Checkbox Styles */
.main-checkbox-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-top: 0.5rem;
}

.checkbox-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #f9f9f9;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  border: 1px solid #e0e0e0;
  transition: all 0.2s ease;
  cursor: pointer;
}

.checkbox-container:hover {
  background-color: #fff5e0;
  border-color: #f8b400;
}

.checkbox-container input[type="checkbox"] {
  width: 1rem;
  height: 1rem;
  color: #f8b400;
}

.checkbox-container label {
  font-size: 0.9rem;
  font-weight: 500;
  color: #555;
  cursor: pointer;
}

.rider-note {
  background-color: #f0f9ff;
  border-radius: 0.75rem;
  padding: 1rem;
  margin: 1rem 0;
  border-left: 3px solid #3498db;
  font-size: 0.9rem;
}

.statistics-section {
  width: 100%;
  padding: 0.5rem;
  background-color: #fff;
  border-radius: 1rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
}

.statistics-section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Order History Styles */
.order-history-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  width: 100%;
}

/* Statistics Section */
.statistics-section-customer {
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
}

.statistics-title,
.spending-title,
.history-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.statistics-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.stat-card {
  background-color: #f8f9fa;
  border-radius: 0.5rem;
  padding: 1rem;
  display: flex;
  gap: 1rem;
  transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  color: #4a6cf7;
  background-color: rgba(74, 108, 247, 0.1);
  padding: 0.5rem;
  border-radius: 50%;
}

.stat-content {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
}

.stat-label {
  font-size: 0.875rem;
  color: #666;
}

/* Spending Section */
.spending-section {
  margin-top: 2rem;
}

.spending-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.spending-card {
  background-color: #f8f9fa;
  border-radius: 0.5rem;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: transform 0.2s, box-shadow 0.2s;
}

.spending-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.spending-icon {
  color: #28a745;
  background-color: rgba(40, 167, 69, 0.1);
  padding: 0.5rem;
  border-radius: 50%;
}

.spending-content {
  display: flex;
  flex-direction: column;
}

.spending-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: #333;
}

.spending-label {
  font-size: 0.875rem;
  color: #666;
}

.order-history-section {
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 0.5rem;
}

.history-orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.empty-history {
  text-align: center;
  padding: 2rem;
  background-color: #f8f9fa;
  border-radius: 0.5rem;
}

.empty-history-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.empty-history-subtitle {
  color: #666;
  margin-top: 0.5rem;
}

/* Pagination Controls */
.pagination-controls {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #eee;
}

.pagination-info {
  font-size: 0.875rem;
  color: #666;
}

.pagination-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.pagination-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-button:hover:not(:disabled) {
  background-color: #e9ecef;
}

.pagination-current {
  font-size: 0.875rem;
  color: #333;
}

.per-page-control {
  display: flex;
  justify-content: flex-end;
}

.per-page-control label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #666;
}

.per-page-select {
  padding: 0.25rem 0.5rem;
  border: 1px solid #ddd;
  border-radius: 0.25rem;
  background-color: #fff;
}

.statistics-card-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #f8b400;
}

.statistics-chart-container {
  background-color: #fff;
  border-radius: 0.75rem;
  padding: 0.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.05);
  border: 1px solid #f0f0f0;
}

.statistics-cards-container {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
}

.statistics-card {
  background-color: #fff;
  border-radius: 0.75rem;
  padding: 1.25rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  border: 1px solid #f0f0f0;
}

.statistics-card:hover {
  transform: translateY(-0.25rem);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
}

.statistics-card-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.statistics-card-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #666;
}

.statistics-card-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.75rem;
}

.statistics-card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: auto;
}

.statistics-card-period {
  font-size: 0.75rem;
  color: #888;
}

.statistics-card-change {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
}

.statistics-card-change.increase {
  color: #4caf50;
  background-color: rgba(76, 175, 80, 0.1);
}

.statistics-card-change.decrease {
  color: #f44336;
  background-color: rgba(244, 67, 54, 0.1);
}

.statistics-chart-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
}

.revenue-chart-wrapper {
  height: 25rem;
  position: relative;
}

.revenue-chart-container {
  width: 100%;
  height: 100%;
}

.statistics-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 15rem;
  font-size: 1rem;
  color: #666;
}

.statistics-error {
  background-color: #ffebee;
  border-radius: 0.75rem;
  padding: 1.25rem;
  margin-bottom: 1.5rem;
}

.statistics-error h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #d32f2f;
  margin-bottom: 0.5rem;
}

.statistics-error p {
  font-size: 0.875rem;
  color: #666;
}

/* Push Notification Settings Styles */
.push-notification-card {
  background-color: #fff;
  border-radius: 1rem;
  box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.1);
  padding: 0.5rem;
  margin: 1rem 0;
  width: 100%;
  max-width: 40rem;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.push-notification-header {
  margin-bottom: 1.5rem;
}

.push-notification-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.push-notification-title h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.push-notification-title-icon {
  color: #f8b400;
}

.push-notification-subtitle {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
}

.push-notification-status {
  margin-bottom: 1.5rem;
  padding: 0.75rem;
  background-color: #f9f9f9;
  border-radius: 0.5rem;
}

.push-notification-status-indicator {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.status-dot {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  background-color: #ccc;
}

.status-dot.active {
  background-color: #4caf50;
  box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.2);
}

.status-dot.inactive {
  background-color: #f44336;
  box-shadow: 0 0 0 0.25rem rgba(244, 67, 54, 0.2);
}

.status-text {
  font-size: 0.9rem;
  font-weight: 500;
}

.push-notification-toggle {
  margin-bottom: 1.5rem;
}

.push-notification-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.875rem;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.push-notification-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.push-notification-enable {
  background-color: #f8b400;
  color: white;
}

.push-notification-enable:hover:not(:disabled) {
  background-color: #e5a700;
  transform: translateY(-0.125rem);
  box-shadow: 0 0.25rem 0.5rem rgba(248, 180, 0, 0.3);
}

.push-notification-disable {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #e0e0e0;
}

.push-notification-disable:hover:not(:disabled) {
  background-color: #ebebeb;
  transform: translateY(-0.125rem);
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.push-notification-button-icon {
  font-size: 1.25rem;
}

.push-notification-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin: 1rem 0;
  padding: 0.75rem;
  background-color: #f9f9f9;
  border-radius: 0.5rem;
  font-size: 0.9rem;
  color: #666;
}

.push-notification-spinner {
  width: 1.25rem;
  height: 1.25rem;
  border: 0.2rem solid rgba(248, 180, 0, 0.2);
  border-top-color: #f8b400;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.push-notification-message {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 1rem 0;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.9rem;
}

.push-notification-message-icon {
  flex-shrink: 0;
}

.push-notification-error {
  background-color: #ffebee;
  color: #d32f2f;
  border-left: 0.25rem solid #d32f2f;
}

.push-notification-success {
  background-color: #e8f5e9;
  color: #2e7d32;
  border-left: 0.25rem solid #2e7d32;
}

.push-notification-details-toggle {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background-color: #f9f9f9;
  border-radius: 0.5rem;
  cursor: pointer;
  margin: 1rem 0;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  color: #555;
}

.push-notification-details-toggle:hover {
  background-color: #f0f0f0;
}

.push-notification-details-icon {
  color: #f8b400;
}

.arrow-icon {
  margin-left: auto;
  width: 0;
  height: 0;
  border-left: 0.4rem solid transparent;
  border-right: 0.4rem solid transparent;
  border-top: 0.4rem solid #666;
  transition: transform 0.3s ease;
}

.arrow-icon.up {
  transform: rotate(180deg);
}

.push-notification-details {
  margin-top: 1rem;
  padding: 1rem;
  background-color: #f9f9f9;
  border-radius: 0.5rem;
  border: 1px solid #f0f0f0;
}

.push-notification-details h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 1rem 0;
}

.push-notification-list {
  list-style: none;
  padding: 0;
  margin: 0 0 1.5rem 0;
}

.push-notification-list-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eee;
}

.push-notification-list-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.push-notification-list-icon {
  color: #f8b400;
  margin-top: 0.25rem;
}

.push-notification-list-item strong {
  display: block;
  font-size: 0.95rem;
  margin-bottom: 0.25rem;
  color: #333;
}

.push-notification-list-item p {
  font-size: 0.85rem;
  color: #666;
  margin: 0;
}

.push-notification-privacy {
  background-color: #fff5e0;
  padding: 1rem;
  border-radius: 0.5rem;
  border-left: 0.25rem solid #f8b400;
}

.push-notification-privacy h4 {
  color: #f8b400;
  margin: 0 0 0.5rem 0;
}

.push-notification-privacy p {
  font-size: 0.85rem;
  color: #666;
  margin: 0;
  line-height: 1.5;
}

.push-notification-not-supported {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 2rem 1rem;
}

.push-notification-icon {
  font-size: 2.5rem;
  color: #f44336;
  margin-bottom: 1rem;
}

.push-notification-not-supported h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.push-notification-not-supported p {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
  line-height: 1.5;
}

/* Main Layout */
.vendor-dashboard,
.rider-dashboard {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f9fa;
  color: #333;
  font-family: 'Poppins', 'Open Sans', sans-serif;
  position: relative;
  overflow-x: hidden;
}

.dashboard-sidebar {
  width: 280px;
  background-color: #fff;
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
  position: fixed;
  height: 100vh;
  overflow-y: auto;
  z-index: 1010;
  transition: transform 0.3s ease, width 0.3s ease;
  left: 0;
  top: 0;
}

.dashboard-nav-badge {
  position: absolute;
  right: 1rem;
  background-color: #ff3b30;
  color: #fff;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 0.1rem 0.4rem;
  border-radius: 10px;
  min-width: 1.2rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  animation: pulse 1.5s infinite;
  box-shadow: 0 0 0 rgba(255, 59, 48, 0.4);
}

.vendor-rating span {
  font-size: 0.875rem;
  font-weight: 600;
  color: #f8b400;
}

.vendor-detail span {
  line-height: 1.3;
  display: inline-block;
  max-width: 90%;
  white-space: normal;
  overflow: visible;
  text-overflow: clip;
  word-break: break-word;
}

.dashboard-sidebar.collapsed {
  width: 80px;
}

.dashboard-sidebar.collapsed .dashboard-logo span,
.dashboard-sidebar.collapsed .dashboard-nav-item span {
  display: none;
}

.dashboard-sidebar.collapsed .dashboard-nav-badge {
  right: 0.5rem;
}

.dashboard-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #f8b400;
  font-weight: 700;
  font-size: 1.25rem;
  transition: all 0.3s ease;
}

.dashboard-logo:hover {
  transform: translateX(5px);
}

.dashboard-nav {
  padding: 0.5rem 0;
}

.dashboard-nav-item {
  width: 95%;
  display: flex;
  align-items: center;
  padding: 0.85rem 1.5rem;
  color: #666;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  margin: 0.4rem;
  border-radius: 0.5rem;
}

.dashboard-nav-item:hover {
  background-color: rgba(248, 180, 0, 0.1);
  color: #f8b400;
  transform: translateX(5px);
}

.dashboard-nav-item.active {
  background-color: rgba(248, 180, 0, 0.15);
  color: #f8b400;
  font-weight: 600;
  border-left: 3px solid #f8b400;
  box-shadow: 0 2px 5px rgba(248, 180, 0, 0.2);
}

.dashboard-nav-icon {
  margin-right: 0.75rem;
  font-size: 1.25rem;
  width: 1.5rem;
  text-align: center;
  color: inherit;
  transition: transform 0.3s ease;
  min-width: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dashboard-nav-item:hover .dashboard-nav-icon {
  transform: scale(1.1);
}

.dashboard-nav-item:hover .dashboard-nav-badge {
  transform: scale(1.1);
  background-color: #ff1a1a;
}

@keyframes pulse-badge {
  0% {
    box-shadow: 0 0 0 0 rgba(244, 67, 54, 0.7);
  }

  50% {
    box-shadow: 0 0 0 6px rgba(244, 67, 54, 0.3);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(244, 67, 54, 0);
  }
}

.dashboard-header {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  position: relative;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
}

.dashboard-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #333;
  margin: 0;
}

.skip {
  font-size: 0.8rem;
  color: #777;
  margin-top: 0.25rem;
  font-style: italic;
}

/* Form Navigation */
.form-navigation {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  margin-top: 1.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid #f0f0f0;
}

.nav-button {
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-button {
  background-color: #f0f0f0;
  color: #555;
  border: none;
}

.back-button:hover {
  background-color: #e0e0e0;
}

.next-button,
.submit-button {
  background-color: #f8b400;
  color: white;
  border: none;
  box-shadow: 0 0.25rem 0.5rem rgba(248, 180, 0, 0.2);
}

.submit-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background-color: #f8b400;
  color: white;
  border: none;
  border-radius: 0.25rem;
  padding: 0.75rem 1.25rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.2s ease;
  align-self: flex-start;
}

.next-button:hover,
.submit-button:hover {
  background-color: #e5a700;
  transform: translateY(-2px);
  box-shadow: 0 0.375rem 0.75rem rgba(248, 180, 0, 0.3);
}

.submit-button:disabled {
  background-color: #ccc;
  transform: none;
  box-shadow: none;
  cursor: not-allowed;
}

.auth-form-footer {
  text-align: center;
  padding: 1.5rem 1.5rem 2rem;
  border-top: 1px solid #f0f0f0;
}

.contact-list a {
  color: #444;
  text-decoration: none;
  transition: color 0.2s ease;
}

.auth-page .account-link {
  font-size: 0.95rem;
  color: #555;
  margin: 0;
}

.auth-page .account-link a {
  color: #f8b400;
  font-weight: 600;
  text-decoration: none;
  transition: color 0.2s ease;
  margin-left: 0.5rem;
}

.contact-list a:hover {
  color: #f8b400;
  text-decoration: underline;
}

.auth-page .account-link a:hover {
  color: #e5a700;
  text-decoration: underline;
}

/* Error Messages */
.error-message {
  color: #e74c3c;
  font-size: 0.85rem;
  margin-top: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background-color: rgba(231, 76, 60, 0.1);
  border-radius: 0.5rem;
  border-left: 3px solid #e74c3c;
}

/* Completion Message */
.completion-message {
  background-color: #e8f5e8;
  color: #2e7d32;
  padding: 1.5rem;
  border-radius: 0.75rem;
  margin: 1rem 0;
  text-align: center;
  border: 1px solid #c8e6c9;
}

.completion-message h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.completion-message p {
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Forgot Password Link */
.forgot-password-link {
  text-align: center;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e0e0e0;
}

.forgot-password-link button {
  background: none;
  border: none;
  color: #f8b400;
  text-decoration: underline;
  cursor: pointer;
  padding: 0.5rem;
  font: inherit;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  border-radius: 0.25rem;
}

.forgot-password-link button:hover {
  color: #e5a700;
  background-color: rgba(248, 180, 0, 0.05);
  text-decoration: none;
}

/* File Input Styling */
.file-input-container {
  position: relative;
  width: 100%;
  margin-bottom: 1rem;
}

.file-input-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background-color: #f9f9f9;
  border: 1px dashed #e0e0e0;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #666;
  font-size: 0.9rem;
}

.file-input-label:hover {
  background-color: #fff5e0;
  border-color: #f8b400;
}

.file-input-icon {
  color: #f8b400;
}

.form-input[type="file"] {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: 0.9rem;
  background-color: #f0f0f0;
  border: 1px solid #e0e0e0;
  border-radius: 0.5rem;
  color: #555;
  cursor: pointer;
  transition: all 0.2s ease;
}

.form-input[type="file"]::-webkit-file-upload-button {
  padding: 0.5rem 1rem;
  margin-right: 1rem;
  background-color: #f8b400;
  color: white;
  border: none;
  border-radius: 0.25rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.form-input[type="file"]:focus {
  outline: none;
  border-color: #f8b400;
  box-shadow: 0 0 0 2px rgba(248, 180, 0, 0.1);
}

.form-input[type="file"]:hover {
  background-color: #fff5e0;
  border-color: #f8b400;
}

.form-input[type="file"]::-webkit-file-upload-button:hover {
  background-color: #e5a700;
}

.form-input[type="file"]::file-selector-button {
  padding: 0.5rem 1rem;
  margin-right: 1rem;
  background-color: #f8b400;
  color: white;
  border: none;
  border-radius: 0.25rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.form-input[type="file"]::file-selector-button:hover {
  background-color: #e5a700;
}

.file-name {
  margin-left: auto;
  font-size: 0.8rem;
  color: #f8b400;
  max-width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Location Alert Styles */
.modal-open {
  overflow: hidden;
}

.location-alert-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  padding: 1rem;
}

.location-alert-modal {
  background-color: white;
  border-radius: 1rem;
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 450px;
  padding: 2rem;
  position: relative;
  text-align: center;
  animation: modal-appear 0.3s ease-out;
}

@keyframes modal-appear {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.location-alert-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  color: #aaa;
  cursor: pointer;
  transition: color 0.2s ease;
  padding: 0.25rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.location-alert-close:hover {
  color: #333;
  background-color: #f5f5f5;
}

.location-alert-icon {
  color: #f8b400;
  margin-bottom: 1rem;
}

.location-alert-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 1.25rem;
}

.location-alert-content {
  margin-bottom: 1.5rem;
}

.location-alert-main {
  font-size: 1.1rem;
  margin-bottom: 1rem;
  color: #333;
  line-height: 1.4;
}

.location-alert-secondary {
  font-size: 1rem;
  color: #666;
  line-height: 1.5;
  margin-bottom: 1.25rem;
}

.location-alert-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #f8f9fa;
  padding: 0.75rem 1rem;
  border-radius: 2rem;
  margin: 0.5rem auto 1.5rem;
  font-size: 1rem;
  color: #555;
  border: 1px solid #e0e0e0;
}

.location-alert-button {
  background-color: #f8b400;
  color: white;
  border: none;
  border-radius: 0.5rem;
  padding: 0.875rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 0.25rem 0.5rem rgba(248, 180, 0, 0.2);
}

.location-alert-button:hover {
  background-color: #e5a700;
  transform: translateY(-2px);
  box-shadow: 0 0.375rem 0.75rem rgba(248, 180, 0, 0.3);
}

.success-message {
  color: rgb(3, 193, 3);
}

.error {
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 9vh;
}

.mobile-money-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 1rem;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
}

.mobile-money-section h4 {
  color: #f8b400;
  text-align: center;
  font-size: 1.1rem;
}

.mobile-money-section p {
  text-align: center;
}

.success-message,
.error-message {
  font-weight: 800;
  text-align: center;
}

@keyframes pulse {
  from {
    transform: scale(0.9);
    opacity: 1;
  }

  to {
    transform: scale(1.8);
    opacity: 0;
  }
}

.home-dashboard-container {
  width: 100%;
  margin: 0 auto;
  padding-bottom: 6rem;
  background-color: #f8f9fa;
}

.hero-section {
  position: relative;
  margin-bottom: 2rem;
  width: 100%;
}

.hero-carousel {
  width: 100%;
  overflow: hidden;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
  border-radius: 0;
}

.home-banner {
  position: relative;
  height: auto;
  margin-top: 4rem;
}

.home-banner img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: brightness(0.85);
}

.home-banner-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: white;
  padding: 1rem;
  z-index: 2;
}

.home-banner-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.home-banner-subtitle {
  font-size: 1rem;
  font-weight: 400;
  max-width: 80%;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.search-container {
  position: absolute;
  bottom: -2rem;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  z-index: 10;
  padding: 0 1rem;
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 2rem;
  padding: 0.5rem 1.25rem;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(248, 180, 0, 0.1);
  transition: all 0.3s ease;
}

.search-icon {
  position: absolute;
  left: 4rem;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
}

.search-container .search-bar .search-icon {
  left: 3rem;
}

.search-bar:focus-within {
  box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
  border-color: rgba(248, 180, 0, 0.3);
}

.search-bar input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 0.95rem;
  color: #333;
  padding: 0.25rem 0;
  margin-left: 1rem;
}

.categories-section {
  margin: 3rem 0 2rem;
  position: relative;
  z-index: 5;
}

.section-header h2::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 0;
  width: 3rem;
  height: 0.25rem;
  background-color: #f8b400;
  border-radius: 1rem;
}

.search-results-info {
  font-size: 0.9rem;
  color: #666;
  font-style: italic;
}

.categories {
  display: flex;
  overflow-x: auto;
  flex-wrap: nowrap;
  gap: 0.75rem;
  padding: 0.5rem 0.25rem;
  scroll-behavior: smooth;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.categories::-webkit-scrollbar {
  display: none;
}

.category-slider {
  display: flex;
  align-items: center;
  width: 100%;
}

.category-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
  padding: 0.5rem;
  border-radius: 1rem;
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
  min-width: 100px;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  cursor: pointer;
}

.category-card:hover {
  transform: translateY(-0.25rem);
  box-shadow: 0 0.5rem 0.75rem rgba(0, 0, 0, 0.1);
}

.category-card.selected {
  background-color: #fff5e0;
  border-color: #f8b400;
  transform: translateY(-0.25rem);
}

.category-icon {
  width: 40px;
  height: 40px;
  margin-bottom: 0.5rem;
}

.category-card p {
  color: #333;
  font-weight: 600;
  font-size: 0.8rem;
  text-align: center;
  margin: 0;
}

.vendors-section {
  margin: 0;
  padding-top: 1rem;
}

.vendor-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.2rem;
  padding: 0.3rem;
}

.no-results p {
  margin: 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.no-results-suggestion {
  font-size: 1rem !important;
  font-weight: normal !important;
  color: #888 !important;
}

.vendor-card {
  background-color: white;
  border-radius: 1rem;
  width: 49%;
  padding: 0.3rem;
  position: relative;
  display: flex;
  flex-direction: column;
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid #f0f0f0;
  margin-bottom: 1rem;
  min-width: 0;
}

.document-modal-body img {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: var(--border-radius-sm);
  box-shadow: var(--shadow-md);
}

.vendor-card:hover {
  transform: translateY(-0.25rem);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.vendor-image {
  display: flex;
  justify-content: center;
  height: 120px; /* Fixed height for consistent image container */
  margin-bottom: 0.5rem;
}

.vendors-dashboard-banner img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: brightness(0.9);
}

.vendor-image img {
  width: 100px;
  height: 100px; /* Fixed height for consistent image size */
  margin-bottom: 0.5rem;
  border-radius: 25%;
  object-fit: cover;
  border: 2px solid #f8b400;
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.food-hero-banner img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: brightness(0.85);
}

.vendor-content {
  flex: 1;
  padding: 0;
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.vendor-header-row {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 0.3rem;
  text-align: center;
}

.vendor-content h2 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.vendor-rating {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff5e0;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  margin: 0 auto;
}

.star-icon {
  color: #f8b400;
  margin-right: 0.25rem;
}

.vendor-description-container {
  position: relative;
  margin-bottom: 0.75rem;
  text-align: center;
  overflow: visible;
}

.vendor-description {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.3;
  transition: max-height 0.3s ease;
  overflow: hidden;
  margin: 0 auto;
  white-space: normal;
  word-break: normal;
}

.vendor-description.collapsed {
  max-height: 2.6rem;
  -webkit-mask-image: linear-gradient(to bottom, black 60%, transparent 100%);
  mask-image: linear-gradient(to bottom, black 60%, transparent 100%);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.vendor-description.expanded {
  max-height: 300px;
  overflow: visible;
  -webkit-mask-image: none;
  mask-image: none;
  display: block;
}

.vendor-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 2.8rem;
  text-align: center;
  border: none;
}

.vendor-detail {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  color: #555;
  overflow: hidden;
  text-overflow: ellipsis;
}

.vendor-closed {
  color: #fff;
  background-color: #ff3b30;
  padding: 0.2rem 0.5rem;
  border-radius: 0.25rem;
  font-weight: 600;
  font-size: 0.8rem;
  display: inline-block;
  box-shadow: 0 2px 4px rgba(255, 59, 48, 0.3);
}

.detail-icon {
  color: #f8b400;
  margin-right: 0.5rem;
  flex-shrink: 0;
  width: 16px;
  height: 16px;
}

.lets-eat-btn,
.see-menu-btn {
  background-color: #f8b400;
  color: white;
  padding: 0.625rem;
  border-radius: 0.5rem;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  border: none;
  box-shadow: 0 2px 4px rgba(248, 180, 0, 0.2);
  margin-top: auto;
  position: absolute;
  bottom: 0.5rem;
  left: 0;
  right: 0;
  width: calc(100% - 1.5rem);
  margin-left: auto;
  margin-right: auto;
}

.lets-eat-btn:hover,
.see-menu-btn:hover {
  background-color: #e5a700;
  transform: translateY(-0.125rem);
  box-shadow: 0 4px 8px rgba(248, 180, 0, 0.3);
}

.see-menu-btn.disabled {
  background-color: #ccc;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

.see-menu-btn.disabled:hover {
  background-color: #ccc;
  transform: none;
  box-shadow: none;
}

.vendor-favorite {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  background-color: white;
  border: none;
  width: 2.25rem;
  height: 2.25rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
  z-index: 5;
}

.vendor-favorite:hover {
  transform: scale(1.1);
}

.heart-icon {
  color: #ff6b6b;
  width: 1.25rem;
  height: 1.25rem;
}

/* Mobile Icons Menu */
.icons-menu {
  width: 100%;
  height: 4.5rem;
  background-color: #f5f5f7;
  box-shadow: 0 -0.25rem 0.5rem rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: center;
  position: fixed;
  bottom: 1rem;
  border-radius: 1rem;
  left: 0;
  padding: 0.5rem 0;
  z-index: 999;
  border-top: 0.1rem solid #f8b400;
  border-bottom: 0.1rem solid #f8b400;
}

.icons-menu ul {
  width: 100%;
  max-width: 500px;
  list-style: none;
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 0 1rem;
  margin: 0;
}

.contact-support p {
  margin-bottom: 1.5rem;
  color: #555;
}

.icon-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border: none;
  cursor: pointer;
  transition: transform 0.2s ease;
  padding: 0.5rem;
  border-radius: 0.5rem;
}

.icon-container img {
  width: 28px;
  height: 28px;
  background-color: transparent;
  margin-bottom: 0.25rem;
  transition: transform 0.2s ease;
}

.contact-details p {
  font-size: 0.9rem;
  margin: 0;
  color: #555;
}

.icon-container p {
  color: #555;
  font-size: 0.75rem;
  text-align: center;
  margin: 0;
  font-weight: 500;
}

.faq-answer p {
  margin: 0;
  line-height: 1.6;
  color: #555;
}

.complaints-header p {
  font-size: 1rem;
  color: #666;
  max-width: 40rem;
  margin: 0 auto;
}

.complaint-response p {
  font-size: 0.875rem;
  color: #555;
  line-height: 1.5;
}

.support-header p {
  font-size: 1rem;
  color: #666;
  max-width: 40rem;
  margin: 0 auto 1.5rem;
}

.suggestions-header p {
  font-size: 1rem;
  color: #666;
  max-width: 40rem;
  margin: 0 auto;
}

.contact-card p {
  font-size: 0.9rem;
  color: #555;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.contact-header p {
  font-size: 1rem;
  color: #666;
  max-width: 40rem;
  margin: 0 auto;
}

.vendors-empty-state p {
  color: #666;
  font-size: 1rem;
  margin: 0.5rem 0;
}

.vendors-food-description p {
  margin: 0;
  white-space: normal;
  overflow: visible;
  word-break: normal;
}

.vendors-status-notice p {
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.5;
}

.status-change-confirmation p {
  margin: 0 0 0.9375rem 0;
  color: #fff;
  font-size: 0.875rem;
}

.vendors-dashboard-title-section p {
  font-size: 1rem;
  color: #666;
}

.order-status-notice p {
  margin: 0;
}

.empty-orders p {
  font-size: 1.25rem;
  color: #333;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.empty-cart p {
  margin: 0.5rem 0;
  color: #555;
  font-size: 1.25rem;
  font-weight: 600;
}

.quick-link-content p {
  font-size: 0.8rem;
  color: #666;
  margin: 0;
}

.icon-container:hover {
  transform: translateY(-0.25rem);
}

.icon-container.active p {
  color: #f8b400;
  font-weight: 600;
}

/* Role Selection Popup Styles */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 1rem;
}

.popup-content {
  display: flex;
  flex-direction: column;
  width: 90%;
  max-width: 400px;
  background-color: white;
  border-radius: 1rem;
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.3);
  overflow: hidden;
  animation: popup-appear 0.3s ease-out;
  position: relative;
}

@keyframes popup-appear {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.popup-header {
  background-color: #f8b400;
  padding: 1.25rem 1.5rem;
  position: relative;
  text-align: center;
}

.popup-title {
  color: white;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
}

.popup-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
  margin-top: 0.25rem;
}

.auth-close {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  border-radius: 50%;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.auth-close:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.popup-body {
  padding: 0.5rem;
}

.role-buttons-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.role-button {
  display: flex;
  align-items: center;
  padding: 1rem;
  background-color: #f9f9f9;
  border: 1px solid #eee;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.role-button:hover {
  background-color: #fff5e0;
  border-color: #f8b400;
  transform: translateY(-2px);
  box-shadow: 0 0.5rem 1rem rgba(248, 180, 0, 0.1);
}

.role-icon {
  background-color: #fff5e0;
  color: #f8b400;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  flex-shrink: 0;
}

.role-icon svg {
  width: 1.5rem;
  height: 1.5rem;
}

.role-info {
  flex: 1;
}

.role-name {
  font-weight: 600;
  font-size: 1rem;
  color: #333;
  margin: 0 0 0.25rem 0;
}

.role-description {
  font-size: 0.8rem;
  color: #666;
  margin: 0;
}

/* Food Dashboard Styles */
.food-dashboard-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding-bottom: 6rem;
}

.food-banner {
  position: relative;
  width: 100%;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.food-hero-banner {
  position: relative;
  height: auto;
  margin-top: 4rem;
}

.vendor-title-container {
  text-align: center;
  margin: -1rem 0 1.5rem 0;
  padding: 1rem;
  position: relative;
  z-index: 5;
}

.vendor-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.5rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.vendor-subtitle {
  font-size: 1rem;
  color: #666;
  font-weight: normal;
}

/* Menu Section */
.food-menu-section {
  padding: 0 0.5rem;
}

.section-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  position: relative;
}

/* Food Grid */
.food-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: space-between;
}

.food-card {
  background-color: white;
  border-radius: 1rem;
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
  width: calc(50% - 0.5rem);
  overflow: hidden;
  margin-bottom: 1rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid #f0f0f0;
}

.food-card:hover {
  transform: translateY(-0.25rem);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.food-image-container {
  width: 100%;
  height: 160px;
  overflow: hidden;
}

.food-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.food-card:hover .food-image {
  transform: scale(1.05);
}

.food-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.5rem 0;
  line-height: 1.3;
}

.food-description-container {
  position: relative;
  margin-bottom: 1rem;
}

.food-description {
  font-size: 1rem;
  color: #666;
  line-height: 1.4;
  transition: max-height 0.3s ease;
  overflow: hidden;
}

.food-description.collapsed {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 2.5rem;
}

.food-description.expanded {
  max-height: 300px;
}

.description-toggle {
  font-size: 0.75rem;
  color: #f8b400;
  background: none;
  border: none;
  padding: 0;
  margin-top: 0.25rem;
  cursor: pointer;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.food-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.food-price {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.price-tag-icon {
  color: #f8b400;
}

.add-to-cart-btn {
  width: 100%;
  padding: 0.75rem;
  background-color: #f8b400;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.2s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
}

.add-to-cart-btn:disabled {
  background-color: #f0f0f0;
  color: #999;
  cursor: not-allowed;
}

.add-to-cart-btn:hover:not(:disabled) {
  background-color: #e5a700;
  transform: translateY(-0.125rem);
}

.no-foods-message {
  text-align: center;
  padding: 3rem 1rem;
  color: #666;
  font-size: 1.1rem;
}

/* Multiple Prices Display */
.multiple-prices-display {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.price-range {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
}

.price-select {
  padding: 0.3rem;
  border: 1px solid #ddd;
  border-radius: 0.25rem;
  font-size: 0.85rem;
  background-color: white;
  cursor: pointer;
}

.price-select:focus {
  outline: none;
  border-color: #f8b400;
  box-shadow: 0 0 0 2px rgba(248, 180, 0, 0.1);
}

/* Cart Price Option Display */
.price-option-display {
  margin-top: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.price-option-label {
  font-size: 0.875rem;
  color: #666;
  font-weight: 500;
}

.price-option-select {
  padding: 0.375rem;
  border: 1px solid #ddd;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  background-color: white;
  cursor: pointer;
}

.price-option-select:focus {
  outline: none;
  border-color: #f8b400;
  box-shadow: 0 0 0 2px rgba(248, 180, 0, 0.1);
}

/* AddFoodForm Multiple Price Styles */
.price-mode-selection {
  margin: 1rem 0;
}

.price-mode-selection h4 {
  margin-bottom: 0.5rem;
  color: #333;
}

.price-mode-selection div {
  display: flex;
  gap: 1rem;
}

.price-mode-selection label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.single-price-section,
.multiple-prices-section {
  margin: 1rem 0;
}

.price-option {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  margin-bottom: 0.75rem;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 0.5rem;
  background-color: #f9f9f9;
}

.price-option input[type="text"],
.price-option input[type="number"] {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 0.25rem;
}

.price-preview {
  font-size: 0.875rem;
  color: #f8b400;
  font-weight: 500;
  margin-top: 0.25rem;
}

.remove-price-btn {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 0.375rem 0.75rem;
  border-radius: 0.25rem;
  cursor: pointer;
  font-size: 0.875rem;
}

.remove-price-btn:hover {
  background-color: #c82333;
}

.add-price-btn {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  cursor: pointer;
  margin-top: 0.5rem;
}

.add-price-btn:hover {
  background-color: #218838;
}

.header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.375rem 0.625rem;
  background-color: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  position: fixed;
  top: 0;
  z-index: 1000;
  border-bottom: 3.5px solid #f8b400;
  height: 4.5rem;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  min-width: 2rem; /* Ensure consistent width when text is hidden */
}

.logo-icon {
  color: #f8b400;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.15));
  font-size: 1.75rem;
}

.logo {
  color: #333;
  font-size: 1.25rem;
  font-weight: 700;
  font-family: 'Poppins', sans-serif;
  letter-spacing: 0.02em;
  margin: 0;
  display: flex;
  align-items: center;
}

.logo-accent {
  color: #f8b400;
  position: relative;
  font-weight: 800;
  text-shadow: 0 1px 2px rgba(248, 180, 0, 0.2);
}

/* Greeting Styles */
.greeting-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.greeting-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.2rem 0.5rem;
  min-width: 8rem;
  border-radius: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(248, 180, 0, 0.1);
  transition: all 0.3s ease;
  backdrop-filter: blur(4px);
  position: relative;
  overflow: hidden;
  background-color: #fff;
}

.greeting-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
  pointer-events: none;
}

.greeting-content:hover {
  transform: translateY(-0.125rem) scale(1.02);
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
}

.greeting-text {
  display: flex;
  align-items: center;
  justify-content: center;
}

.greeting-stack {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.125rem;
}

.greeting-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  font-weight: 500;
  letter-spacing: 0.02em;
  margin-bottom: 0.25rem;
  font-family: 'Poppins', sans-serif;
  color: #666;
}

.greeting-time svg {
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  color: #f8b400;
}

.user-name {
  color: #333;
  font-size: 1.1rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  text-align: center;
  position: relative;
  letter-spacing: 0.01em;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
  font-family: 'Poppins', sans-serif;
}

/* Cart Button Styles */
.cart-button {
  background-color: #f8b400;
  border: none;
  border-radius: 20%;
  width: 2.5rem;
  height: 2.5rem;
  cursor: pointer;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  color: white;
  box-shadow: 0 2px 8px rgba(248, 180, 0, 0.3);
}

.cart-button:hover {
  background-color: #e5a700;
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(248, 180, 0, 0.4);
}

.cart-icon {
  color: white;
}

.cart-badge {
  position: absolute;
  top: -0.25rem;
  right: -0.25rem;
  background-color: #f44;
  color: white;
  border-radius: 50%;
  width: 1.5rem;
  height: 1.5rem;
  font-size: 0.8rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.menu-dropdown {
  display: flex !important;
  flex-direction: column;
  align-items: stretch !important;
  position: absolute;
  bottom: 100%;
  right: 1rem;
  width: 200px !important;
  background-color: #fff;
  border-radius: 0.75rem;
  box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.15);
  padding: 0.5rem 0;
  overflow: hidden;
  border: 0.0625rem solid #f0f0f0;
}

.menu-dropdown li {
  width: 100%;
  margin: 0;
  padding: 0;
}

.section-content button {
  width: 100%;
  padding: 1rem;
  background-color: #f8b400;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.2s ease;
}

.menu-dropdown li button {
  width: 100%;
  text-align: left;
  padding: 0.75rem 1rem;
  background-color: transparent;
  border: none;
  font-size: 0.9rem;
  color: #555;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
}

.section-content button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.add-food-form button:disabled {
  background-color: #aaa;
  cursor: not-allowed;
}

.menu-dropdown li:last-child button {
  border-top: 0.0625rem solid #f0f0f0;
  color: #f44;
}

.menu-dropdown li button:hover {
  background-color: #f9f9f9;
  color: #f8b400;
}

.section-content button:hover:not(:disabled) {
  background-color: #e5a700;
  transform: translateY(-0.125rem);
}

.add-food-form button:hover:not(:disabled) {
  background-color: #0056b3;
}

.menu-dropdown li:last-child button:hover {
  background-color: #fff5f5;
  color: #f44;
}

.cart-container {
  width: 95%;
  max-width: 75rem;
  margin: 5rem auto;
  padding: 0.5rem;
  background-color: #fff;
  border-radius: 0.75rem;
  box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.1);
}

.cart-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.3rem;
  border-bottom: 0.0625rem solid #f0f0f0;
}

.cart-header-icon {
  color: #f8b400;
  margin-right: 1rem;
  font-size: 1.5rem;
}

.cart-title {
  font-size: 1.75rem;
  margin: 0;
  color: #333;
  font-weight: 600;
}

.cart-inner {
  margin-bottom: 3rem;
  padding-bottom: 1rem;
}

.cart-items {
  list-style: none;
  padding: 0;
  margin: 0 0 1.5rem 0;
}

.cart-item {
  display: flex;
  flex-direction: column;
  padding: 0.5rem;
  margin-bottom: 1rem;
  border: 0.0625rem solid #f0f0f0;
  border-radius: 0.75rem;
  background-color: #fff;
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.cart-item:hover {
  transform: translateY(-0.25rem);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
}

.cart-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.cart-item-image-container {
  width: 25%;
  max-width: 6rem;
  border-radius: 0.5rem;
  overflow: hidden;
}

.cart-food-image {
  width: 100%;
  height: auto;
  border-radius: 0.5rem;
  object-fit: cover;
  aspect-ratio: 1/1;
}

.cart-item-details {
  flex: 1;
  padding: 0 1rem;
}

.cart-item-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.25rem 0;
}

.cart-item-description {
  font-size: 0.875rem;
  color: #777;
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 2.8rem;
}

.price-display {
  display: flex;
  align-items: center;
  font-size: 1rem;
  color: #555;
}

.price-label {
  margin-right: 0.5rem;
  font-weight: 500;
}

.price-value {
  color: #f8b400;
  font-weight: 600;
}

.cart-item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 0.0625rem solid #f0f0f0;
}

.cart-controls {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.quantity-button {
  background-color: #f8f8f8;
  border: 0.0625rem solid #e0e0e0;
  border-radius: 50%;
  width: 2rem;
  height: 2rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.quantity-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity-button:hover:not(:disabled) {
  background-color: #f0f0f0;
}

.quantity-value {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  min-width: 1.5rem;
  text-align: center;
}

.item-subtotal {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.subtotal-label {
  font-size: 0.875rem;
  color: #777;
  margin-bottom: 0.25rem;
}

.subtotal-value {
  font-size: 1.125rem;
  font-weight: 600;
  color: #333;
}

.cart-summary {
  background-color: #f9f9f9;
  border-radius: 0.75rem;
  padding: 0.8rem;
  margin-bottom: 1rem;
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 10;
}

.total-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1.25rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 0.0625rem solid #e0e0e0;
}

.total-label {
  font-weight: 600;
  color: #333;
}

.cart-actions {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.action-button {
  flex: 1;
  padding: 0.5rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  min-height: 3rem;
  margin-bottom: 0.5rem;
}

.clear-button {
  background-color: #f8f8f8;
  color: #777;
  border: 0.0625rem solid #e0e0e0;
}

.clear-button:hover {
  background-color: #f0f0f0;
  color: #555;
}

.order-button {
  background-color: #f8b400;
  color: white;
}

.order-button:hover {
  background-color: #e5a700;
  transform: translateY(-0.125rem);
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.remove-item {
  background: none;
  border: none;
  cursor: pointer;
  color: #aaa;
  padding: 0.5rem;
  transition: color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-item:hover {
  color: #f44;
}

.empty-cart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
}

.empty-cart-icon {
  color: #ddd;
  margin-bottom: 1.5rem;
}

.empty-cart-subtext {
  color: #888 !important;
  font-size: 1rem !important;
  font-weight: normal !important;
}

/* Professional Food Delivery Order Form */
.order-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: overlayFadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes overlayFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0);
  }

  to {
    opacity: 1;
    backdrop-filter: blur(12px);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

#rider-select {
  border: none;
  width: 100%;
  height: 6vh;
  padding: 0 0.5rem;
  border-radius: 10px;
  font-weight: 900;
}

/* Wizard Container */

/* Modern Food Delivery Wizard */
.order-form-wizard {
  background: var(--text-white);
  border-radius: 24px;
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.12),
    0 16px 32px rgba(0, 0, 0, 0.08),
    0 8px 16px rgba(0, 0, 0, 0.04);
  width: 100%;
  max-width: 800px;
  height: 100vh;
  overflow: hidden;
  position: relative;
  animation: wizardSlideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes wizardSlideUp {
  from {
    opacity: 0;
    transform: translateY(60px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Wizard Header */

/* Modern Food Delivery Header */
.wizard-header {
  background: #f8b400;
  color: var(--text-white);
  padding: 2.5rem 2rem;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-shrink: 0;
  overflow: hidden;
}

.wizard-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  opacity: 0.3;
}

.header-content {
  flex: 1;
  position: relative;
  z-index: 2;
}

.wizard-title {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 1.75rem;
  font-weight: 800;
  margin: 0 0 0.75rem 0;
  font-family: 'Poppins', sans-serif;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.title-icon {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.75rem;
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.title-icon {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem;
  border-radius: var(--radius-md);
}

.wizard-subtitle {
  opacity: 0.95;
  text-align: center;
  font-size: 1.1rem;
  line-height: 1.5;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.close-wizard {
  background: rgba(255, 255, 255, 0.15);
  border: none;
  color: var(--text-white);
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-left: 1rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 2;
}

.close-wizard:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.1) rotate(90deg);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Modern Progress Section */
.wizard-progress {
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
  position: relative;
}

.wizard-progress::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.8) 50%, transparent 100%);
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: #e2e8f0;
  border-radius: 8px;
  margin-bottom: 2rem;
  overflow: hidden;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
}

.progress-fill {
  height: 100%;
  background: #f8b400;
  border-radius: 8px;
  transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
  animation: progressShimmer 2s infinite;
}

@keyframes progressShimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-steps {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 450px;
  margin: 0 auto;
  position: relative;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.step-number {
  width: 1.2rem;
  height: 1.2rem;
  border-radius: 50%;
  background: #e2e8f0;
  color: #64748b;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 800;
  font-size: 1.1rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  border: 3px solid #e2e8f0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.step-number::before {
  content: '';
  position: absolute;
  top: -6px;
  left: -6px;
  right: -6px;
  bottom: -6px;
  border-radius: 50%;
  background: transparent;
  transition: all 0.4s ease;
}

.progress-step.active .step-number {
  background: linear-gradient(135deg, #0ebce9 0%, #06b6d4 100%);
  color: var(--text-white);
  border-color: #0ebce9;
  transform: scale(1.15);
  box-shadow:
    0 0 0 8px rgba(14, 188, 233, 0.15),
    0 8px 25px rgba(14, 188, 233, 0.25);
  animation: activePulse 2s infinite;
}

.progress-step.completed .step-number {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: var(--text-white);
  border-color: #10b981;
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3);
  animation: completedBounce 0.6s ease-out;
}

.progress-step.pending .step-number {
  background: #f1f5f9;
  color: #94a3b8;
  border-color: #e2e8f0;
}

.step-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #64748b;
  text-align: center;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.progress-step.active .step-label {
  color: #0ebce9;
  font-weight: 700;
  transform: scale(1.05);
}

.progress-step.completed .step-label {
  color: #10b981;
  font-weight: 700;
}

.progress-step.pending .step-label {
  color: #94a3b8;
}

@keyframes activePulse {
  0%,
  100% {
    box-shadow:
      0 0 0 8px rgba(14, 188, 233, 0.15),
      0 8px 25px rgba(14, 188, 233, 0.25);
  }

  50% {
    box-shadow:
      0 0 0 12px rgba(14, 188, 233, 0.1),
      0 8px 25px rgba(14, 188, 233, 0.35);
  }
}

@keyframes completedBounce {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1.1); }
}

@keyframes stepPulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.2;
  }

  50% {
    transform: scale(1.2);
    opacity: 0.1;
  }
}

.progress-step.completed .step-number {
  background: linear-gradient(135deg, var(--success) 0%, #16a34a 100%);
  color: var(--text-white);
  transform: scale(1.1);
  border-color: rgba(34, 197, 94, 0.3);
  animation: stepComplete 0.6s ease-out;
}

@keyframes stepComplete {
  0% {
    transform: scale(1) rotate(0deg);
  }

  50% {
    transform: scale(1.3) rotate(180deg);
  }

  100% {
    transform: scale(1.1) rotate(360deg);
  }
}

.step-label {
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--text-secondary);
  text-align: center;
}

.progress-step.active .step-label,
.progress-step.completed .step-label {
  color: var(--text-primary);
  font-weight: 600;
}

.progress-line {
  flex: 1;
  height: 3px;
  background: var(--gray-300);
  margin: 0 1.5rem;
  position: relative;
  top: -1.5rem;
  border-radius: 2px;
  overflow: hidden;
}

.progress-line::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 0;
  background: linear-gradient(90deg, var(--success) 0%, #16a34a 100%);
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 2px;
}

.progress-step.completed + .progress-line {
  background: var(--gray-300);
}

.progress-step.completed + .progress-line::before {
  width: 100%;
  animation: progressFill 0.8s ease-out;
}

/* Modern Content Area */
.wizard-content {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem;
  background: var(--text-white);
  position: relative;
}

.wizard-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(0, 0, 0, 0.05) 50%, transparent 100%);
}

/* Modern Step Content */
.step-content {
  animation: stepSlideIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  max-width: 600px;
  margin: 0 auto;
}

@keyframes stepSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.step-header {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 1rem;
  padding: 0.5rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 20px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.step-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23000000' fill-opacity='0.02'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E") repeat;
  opacity: 0.5;
}

.step-icon {
  background: #f8b400;
  color: var(--text-white);
  padding: 0.5rem;
  border-radius: 16px;
  flex-shrink: 0;
  position: relative;
  z-index: 2;
}

.step-title {
  font-size: 1.5rem;
  font-weight: 800;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
  font-family: 'Poppins', sans-serif;
  position: relative;
  z-index: 2;
}

.step-description {
  font-size: 1rem;
  color: #64748b;
  margin: 0;
  line-height: 1.6;
  position: relative;
  z-index: 2;
}

/* Modern Form Styling */
.step-form {
  max-width: 100%;
}

.form-group {
  margin-bottom: 2rem;
}

.form-label {
  display: block;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.75rem;
  font-size: 1rem;
  font-family: 'Poppins', sans-serif;
}

.form-input,
.form-select {
  width: 100%;
  padding: 1rem 1.25rem;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  background: #f8fafc;
  color: #1e293b;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  padding-left: 2rem;
}

.form-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1rem;
  padding-right: 3rem;
}

/* Disabled Form Elements */
.form-input:disabled,
.form-select:disabled {
  background: #f1f5f9;
  color: #94a3b8;
  cursor: not-allowed;
  border-color: #e2e8f0;
  transform: none;
  opacity: 0.7;
}

.form-input:disabled::placeholder {
  color: #cbd5e1;
}

/* Town Selection Specific Styles */
.form-group .form-select[value=""] {
  color: #94a3b8;
}

.prefixed-input .input-prefix {
  background: #e2e8f0;
  color: #64748b;
  font-weight: 600;
  white-space: nowrap;
  border-right: 1px solid #cbd5e1;
  padding: 1rem 1.25rem;
  border-radius: 16px 0 0 16px;
}

.prefixed-input .form-input {
  border-radius: 0 16px 16px 0;
  border-left: none;
  padding-left: 1rem;
}

/* Validation Error Styling */
.validation-error {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(239, 68, 68, 0.05) 100%);
  border: 1px solid #fecaca;
  border-radius: 12px;
  color: #dc2626;
  font-size: 0.875rem;
  font-weight: 500;
  animation: errorSlideIn 0.3s ease-out;
}

.validation-error svg {
  color: #dc2626;
  flex-shrink: 0;
}

@keyframes errorSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Modern Radio Group */
.radio-group {
  display: flex;
  justify-content: space-between;
  margin-top: 0.75rem;
}

.radio-option {
  width: 45%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 0.8rem;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: #f8fafc;
  position: relative;
  overflow: hidden;
}

.radio-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #0ebce9 0%, #06b6d4 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.radio-option:hover {
  background: rgba(14, 188, 233, 0.05);
  transform: translateY(-2px);
}

.radio-option input[type="radio"] {
  margin: 0;
  accent-color: #0ebce9;
  transform: scale(1.2);
  position: relative;
  z-index: 2;
}

.radio-option:has(input[type="radio"]:checked) {
  border-color: #f8b400;
  background: var(--text-white);
  color: #0ebce9;
  font-weight: 700;
  transform: translateY(-2px);
}

.radio-option:has(input[type="radio"]:checked)::before {
  opacity: 0.05;
}

.radio-label {
  font-weight: 600;
  font-size: 1rem;
  position: relative;
  z-index: 2;
}

/* Address Input Group */
.address-input-group {
  position: relative;
}

.prefixed-input {
  display: flex;
  align-items: center;
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-md);
  background: var(--bg-secondary);
  transition: all 0.3s ease;
}

.prefixed-input:focus-within {
  border-color: var(--primary-blue);
}

.input-prefix {
  background: var(--gray-100);
  padding: 0.875rem 1rem;
  color: var(--text-secondary);
  font-weight: 500;
  white-space: nowrap;
  border-right: 1px solid var(--gray-200);
}

.prefixed-input .form-input {
  border: none;
  border-radius: 0 var(--radius-md) var(--radius-md) 0;
  box-shadow: none;
}

.prefixed-input .form-input:focus {
  box-shadow: none;
  border: none;
}

/* Recipient Details */
.recipient-details {
  display: flex;
  flex-direction: column;
}

/* Modern Navigation */
.wizard-navigation {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding: 0.5rem;
  flex-shrink: 0;
  position: relative;
}

.wizard-navigation::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.8) 50%, transparent 100%);
}

.nav-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.nav-spacer {
  flex: 1;
}

.nav-button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  border: none;
  border-radius: 16px;
  font-weight: 700;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: 'Poppins', sans-serif;
  position: relative;
  overflow: hidden;
}

.back-button {
  background: var(--text-white);
  color: #64748b;
  border: 2px solid #e2e8f0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.back-button:hover {
  background: #f8fafc;
  color: #1e293b;
  border-color: #cbd5e1;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.next-button,
.complete-button {
  color: var(--text-white);
  border: none;
  position: relative;
}

.next-button::before,
.complete-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.next-button:hover,
.complete-button:hover {
  transform: translateY(-3px);
}

.next-button:hover::before,
.complete-button:hover::before {
  opacity: 1;
}

.nav-button.disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05) !important;
}

.nav-button.disabled::before {
  opacity: 0 !important;
}

.button-icon.rotated {
  transform: rotate(90deg);
}

.button-icon {
  position: relative;
  z-index: 2;
}

/* Modern Error Display */
.wizard-error {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(239, 68, 68, 0.05) 100%);
  border: 2px solid #fecaca;
  border-radius: 16px;
  margin: 1.5rem 2.5rem;
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
}

.wizard-error::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ef4444' fill-opacity='0.03'%3E%3Ccircle cx='10' cy='10' r='2'/%3E%3C/g%3E%3C/svg%3E") repeat;
  opacity: 0.5;
}

.error-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: #dc2626;
  font-weight: 600;
  position: relative;
  z-index: 2;
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
  .order-form-wizard {
    max-width: 95vw;
    margin: 0.5rem;
    border-radius: 20px;
  }

  .wizard-header {
    padding: 2rem 1.5rem;
  }

  .wizard-title {
    font-size: 1.5rem;
  }

  .wizard-subtitle {
    font-size: 1rem;
  }

  .close-wizard {
    width: 2.5rem;
    height: 2.5rem;
  }

  .progress-steps {
    max-width: 100%;
  }

  .step-label {
    font-size: 0.8rem;
  }

  .step-title {
    font-size: 1.25rem;
  }

  .step-description {
    font-size: 0.95rem;
  }

  .form-input,
  .form-select {
    border-radius: 12px;
    font-size: 0.95rem;
  }

  .nav-spacer {
    display: none;
  }

  .nav-button {
    width: 100%;
    justify-content: center;
    padding: 1rem 1.5rem;
    border-radius: 12px;
  }

  .wizard-error {
    margin: 1rem;
    padding: 1rem;
    border-radius: 12px;
  }
}

@media (max-width: 480px) {
  .order-form-wizard {
    max-width: 100vw;
    margin: 0;
    border-radius: 0;
  }

  .wizard-header {
    padding: 1rem;
  }

  .wizard-title {
    font-size: 1.25rem;
  }

  .step-title {
    font-size: 1.1rem;
  }

  .form-input,
  .form-select {
    font-size: 0.9rem;
  }

  .nav-button {
    padding: 0.875rem 1.25rem;
    font-size: 0.9rem;
  }
}

@keyframes progressFill {
  0% {
    width: 0;
    background: var(--primary-blue);
  }

  50% {
    background: var(--primary-blue);
  }

  100% {
    width: 100%;
    background: var(--success);
  }
}

/* Order Form Content */
.order-form-content {
  padding: 0.5rem;
}

/* Order Sections */
.order-section {
  background: var(--bg-secondary);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-xl);
  margin-bottom: 1.5rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.order-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gray-200);
  transition: all 0.4s ease;
}

.order-section.expanded {
  border-color: var(--primary-blue);
  box-shadow: 0 8px 32px rgba(14, 188, 233, 0.15);
  transform: translateY(-2px);
}

.order-section.expanded::before {
  background: var(--gradient-primary);
}

.order-section.completed {
  border-color: var(--success);
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.03) 0%, rgba(34, 197, 94, 0.01) 100%);
  box-shadow: 0 4px 16px rgba(34, 197, 94, 0.1);
}

.order-section.completed::before {
  background: var(--success);
}

.order-section.disabled {
  opacity: 0.5;
  pointer-events: none;
  filter: grayscale(0.3);
}

.section-header {
  padding: 2rem 1.5rem;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: var(--bg-secondary);
  position: relative;
}

.section-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 1.5rem;
  right: 1.5rem;
  height: 1px;
  background: var(--gray-200);
  transition: all 0.3s ease;
}

.section-header:hover {
  background: var(--bg-light);
  transform: translateY(-1px);
}

.order-section.expanded .section-header {
  background: linear-gradient(135deg, rgba(14, 188, 233, 0.08) 0%, rgba(14, 188, 233, 0.03) 100%);
}

.order-section.expanded .section-header::after {
  background: var(--primary-blue);
  height: 2px;
}

.order-section.completed .section-header {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.08) 0%, rgba(34, 197, 94, 0.03) 100%);
  cursor: pointer;
  position: relative;
}

.order-section.completed .section-header:hover::before {
  content: 'Click to edit';
  position: absolute;
  top: -2.5rem;
  right: 1rem;
  background: var(--text-primary);
  color: var(--text-white);
  padding: 0.5rem 0.75rem;
  border-radius: var(--radius-md);
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
  z-index: 1000;
  animation: tooltipFadeIn 0.2s ease-out;
}

.order-section.completed .section-header:hover::after {
  content: '';
  position: absolute;
  top: -0.5rem;
  right: 1.5rem;
  width: 0;
  height: 0;
  border-left: 0.5rem solid transparent;
  border-right: 0.5rem solid transparent;
  border-top: 0.5rem solid var(--text-primary);
  z-index: 1000;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.order-section.completed .section-header::after {
  background: var(--success);
  height: 2px;
}

/* Manual Edit Indicator */
.order-section.manually-opened {
  border-left: 4px solid var(--warning);
}

.order-section.manually-opened .section-header {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.08) 0%, rgba(251, 191, 36, 0.03) 100%);
}

.order-section.manually-opened .section-header::after {
  background: var(--warning);
  height: 2px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.section-icon {
  width: 3.5rem;
  height: 3.5rem;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.section-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.section-header:hover .section-icon::before {
  opacity: 1;
}

.delivery-icon {
  background: linear-gradient(135deg, rgba(14, 188, 233, 0.15) 0%, rgba(14, 188, 233, 0.08) 100%);
  color: var(--primary-blue);
}

.rider-icon {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.15) 0%, rgba(34, 197, 94, 0.08) 100%);
  color: var(--success);
}

.payment-icon {
  background: linear-gradient(135deg, rgba(255, 0, 12, 0.15) 0%, rgba(255, 0, 12, 0.08) 100%);
  color: var(--primary-red);
}

.order-section.completed .section-icon {
  background: linear-gradient(135deg, var(--success) 0%, #16a34a 100%);
  color: var(--text-white);
  transform: scale(1.05);
}

.order-section.expanded .section-icon {
  transform: scale(1.1);
}

.section-info {
  flex: 1;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 0.25rem 0;
  font-family: 'Poppins', sans-serif;
}

.section-description {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.4;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.completion-badge {
  background: linear-gradient(135deg, var(--success) 0%, #16a34a 100%);
  color: var(--text-white);
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: completionPulse 0.8s ease-out;
  box-shadow: 0 0 4px rgba(34, 197, 94, 0.3);
  position: relative;
}

.completion-badge::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--success) 0%, #16a34a 100%);
  opacity: 0.3;
  animation: completionRipple 1.5s infinite;
}

@keyframes completionPulse {
  0% {
    opacity: 0;
    transform: scale(0.3) rotate(-180deg);
  }

  50% {
    opacity: 1;
    transform: scale(1.2) rotate(0deg);
  }

  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

@keyframes completionRipple {
  0% {
    transform: scale(1);
    opacity: 0.3;
  }

  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

.lock-badge {
  background: linear-gradient(135deg, var(--gray-300) 0%, var(--gray-400) 100%);
  color: var(--text-secondary);
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.expand-arrow {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--text-secondary);
  background: rgba(0, 0, 0, 0.05);
  border-radius: 50%;
  padding: 0.5rem;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.expand-arrow.rotated {
  transform: rotate(180deg);
  color: var(--primary-blue);
  background: rgba(14, 188, 233, 0.1);
}

.section-header:hover .expand-arrow {
  background: rgba(14, 188, 233, 0.1);
  color: var(--primary-blue);
  transform: scale(1.1);
}

.section-header:hover .expand-arrow.rotated {
  transform: rotate(180deg) scale(1.1);
}

/* Section Content */
.section-content {
  padding: 0 2rem 2rem 2rem;
  background: var(--bg-secondary);
  animation: slideDown 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-top: 1px solid var(--gray-200);
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
    max-height: 0;
  }

  to {
    opacity: 1;
    transform: translateY(0);
    max-height: 1000px;
  }
}

.order-section.expanded .section-content {
  background: linear-gradient(135deg, rgba(14, 188, 233, 0.03) 0%, rgba(14, 188, 233, 0.01) 100%);
  border-top-color: rgba(14, 188, 233, 0.2);
}

.order-section.completed .section-content {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.03) 0%, rgba(34, 197, 94, 0.01) 100%);
  border-top-color: rgba(34, 197, 94, 0.2);
}

/* Delivery Type Styles */
.delivery-type {
  margin-bottom: 1.5rem;
}

.order-for {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-family: 'Poppins', sans-serif;
}

.types-input {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.types-input label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: var(--transition-fast);
  background: var(--bg-secondary);
  font-weight: 500;
  flex: 1;
  justify-content: center;
}

.types-input label:hover {
  border-color: var(--primary-blue);
  background: rgba(14, 188, 233, 0.05);
}

.types-input input[type="radio"] {
  margin: 0;
  accent-color: var(--primary-blue);
}

.types-input input[type="radio"]:checked + label,
.types-input label:has(input[type="radio"]:checked) {
  border-color: var(--primary-blue);
  background: rgba(14, 188, 233, 0.1);
  color: var(--primary-blue);
  font-weight: 600;
}

/* Delivery Location Select */
.delivery-location select {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-md);
  background: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 0.95rem;
  font-weight: 500;
  transition: var(--transition-fast);
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1rem;
  padding-right: 3rem;
}

.delivery-location select:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 4px rgba(14, 188, 233, 0.1);
}

/* Input Containers */
.address-input-container,
.input-container {
  position: relative;
  margin-bottom: 1rem;
}

.input-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  z-index: 2;
}

.address-input,
.recipient-input {
  width: 100%;
  padding: 1rem 1rem 1rem 3.5rem;
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-lg);
  background: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.address-input:focus,
.recipient-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 4px rgba(14, 188, 233, 0.15);
  background: rgba(14, 188, 233, 0.02);
  transform: translateY(-2px);
}

.address-input:focus + .input-icon,
.recipient-input:focus + .input-icon {
  color: var(--primary-blue);
  transform: scale(1.1);
}

.address-input::placeholder,
.recipient-input::placeholder {
  color: var(--text-secondary);
}

/* Dunkwa Address Container */
.dunkwa-address-container {
  display: flex;
  align-items: center;
  width: 100%;
}

.dunkwa-prefix {
  background: var(--gray-100);
  border: 2px solid var(--gray-200);
  border-right: none;
  border-radius: var(--radius-md) 0 0 var(--radius-md);
  padding: 0.875rem 1rem;
  color: var(--text-secondary);
  font-weight: 500;
  white-space: nowrap;
}

.address-input.with-prefix,
.recipient-input.with-prefix {
  border-radius: 0 var(--radius-md) var(--radius-md) 0;
  border-left: none;
  padding-left: 1rem;
}

/* Recipient Form */
.recipient-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Rider Selection Container */
.rider-selection-container {
  background: var(--bg-light);
  border-radius: var(--radius-md);
  padding: 1rem;
  border: 1px solid var(--gray-200);
}

/* Payment Container */
.payment-container {
  background: var(--bg-light);
  border-radius: var(--radius-md);
  padding: 1rem;
  border: 1px solid var(--gray-200);
}

/* Error Message */
.order-form .error-message {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid var(--error);
  color: var(--error);
  padding: 1rem;
  border-radius: var(--radius-md);
  margin: 1rem 2rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .order-form {
    max-width: 95vw;
    margin: 0.5rem;
  }

  .order-form-header {
    padding: 1rem;
  }

  .order-form-title {
    font-size: 1.25rem;
  }

  .order-progress {
    padding: 1rem;
  }

  .progress-steps {
    max-width: 300px;
  }

  .step-label {
    font-size: 0.7rem;
  }

  .order-form-content {
    padding: 0.5rem;
  }

  .section-header {
    padding: 1rem;
  }

  .section-icon {
    width: 2.5rem;
    height: 2.5rem;
  }

  .section-title {
    font-size: 1rem;
  }

  .section-description {
    font-size: 0.85rem;
  }

  .types-input {
    flex-direction: column;
    gap: 0.75rem;
  }

  .dunkwa-prefix {
    padding: 0.75rem;
    font-size: 0.9rem;
  }

  .address-input,
  .recipient-input {
    padding: 0.75rem 0.75rem 0.75rem 2.5rem;
    font-size: 0.9rem;
  }

  .delivery-location select {
    padding: 0.75rem;
    font-size: 0.9rem;
  }
}

/* Success Celebration Animation */
.order-form.all-completed {
  animation: celebrationPulse 1s ease-out;
}

@keyframes celebrationPulse {
  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.02);
  }
}

.order-form.all-completed .order-progress {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(34, 197, 94, 0.05) 100%);
}

/* Confetti Animation for Completion */
.order-form.all-completed::after {
  content: '🎉';
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 2rem;
  animation: confetti 2s ease-out;
  pointer-events: none;
}

@keyframes confetti {
  0% {
    opacity: 0;
    transform: translateY(20px) rotate(0deg) scale(0);
  }

  50% {
    opacity: 1;
    transform: translateY(-10px) rotate(180deg) scale(1.2);
  }

  100% {
    opacity: 0;
    transform: translateY(-30px) rotate(360deg) scale(0.8);
  }
}

#mobile_money_provider {
  width: 100%;
}

.order-form #rider-select,
#mobile_money_provider,
.delivery-zone-select {
  width: 100%;
  border: 0.0625rem solid #e0e0e0;
  border-radius: 0.5rem;
  font-size: 1rem;
  color: #555;
  outline: none;
  background-color: #f9f9f9;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.order-form #rider-select:focus,
#mobile_money_provider:focus,
.delivery-zone-select:focus {
  border-color: #f8b400;
  box-shadow: 0 0 0 0.125rem rgba(248, 180, 0, 0.2);
}

.order-form .order-for {
  color: #555;
  font-size: 1rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
}

.types-input {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.radio-label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.radio-text {
  margin-left: 0.5rem;
  color: #555;
}

.input-container,
.address-input-container {
  position: relative;
  margin-bottom: 1rem;
}

.order-form input,
.address-input,
.recipient-input {
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  border: 0.0625rem solid #e0e0e0;
  border-radius: 0.5rem;
  font-size: 1rem;
  background-color: #f9f9f9;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.dunkwa-address-container {
  display: flex;
  align-items: center;
  width: 100%;
  position: relative;
  background-color: #f9f9f9;
  border: 0.0625rem solid #e0e0e0;
  border-radius: 0.5rem;
  overflow: hidden;
}

.dunkwa-prefix {
  display: flex;
  align-items: center;
  padding-left: 0.5rem;
  font-weight: 600;
  color: #333;
  background-color: rgba(248, 180, 0, 0.1);
  height: 100%;
  white-space: nowrap;
}

.address-input.with-prefix,
.recipient-input.with-prefix {
  border: none;
  border-radius: 0;
  border-left: 0.0625rem solid #e0e0e0;
  padding-left: 0.75rem;
}

.add-food-form input,
.add-food-form textarea {
  width: 100%;
  padding: 1rem;
  font-size: 1rem;
  border: 0.1rem solid #ccc;
  border-radius: 0.5rem;
  box-sizing: border-box;
  background-color: #fff;
}

.add-food-form textarea {
  resize: none;
  height: 8rem;
}

.add-food-form input[type="file"] {
  font-size: 1rem;
  padding: 0.5rem;
}

.contact-form input,
.contact-form textarea,
.suggestion-textarea,
.complaint-textarea {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 0.0002rem solid #e0e0e040;
  border-bottom: 0.125rem solid #f8b400;
  border-radius: 0.5rem;
  font-size: 1rem;
  background-color: #fff;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-group input {
  width: 100%;
  padding: 10px;
  border-radius: 5px;
  border: 0 solid #e0e0e0 !important;
  background-color: #f9f9f9 !important;
  color: #333;
  border-bottom: 0.125rem solid #f8b400 !important;
}

.order-form input:focus,
.address-input:focus,
.recipient-input:focus {
  border-color: #f8b400;
  outline: none;
  box-shadow: 0 0 0 0.125rem rgba(248, 180, 0, 0.2);
}

.close-order-form {
  position: absolute;
  top: 0.7rem;
  right: 4rem;
  background: transparent;
  border: none;
  font-size: 1.5rem;
  color: #fff;
  cursor: pointer;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease, color 0.2s ease;
  z-index: 10;
}

.close-order-form:hover {
  background-color: #f0f0f0;
  color: #555;
}

.delivery-type {
  margin: 1.5rem auto;
}

.delivery-type .types-input {
  display: flex;
  justify-content: flex-start;
  color: #555;
}

.loading-riders {
  color: #555;
  text-align: center;
}

.rider-details {
  margin: 3% auto;
}

.rider-details img {
  width: 70px;
  border-radius: 0.5rem;
}

.rider-details,
.rider-sub-details {
  display: flex;
  gap: 3%;
}

.rider-details .rider-sub-details {
  flex-direction: column;
  align-items: flex-start;
  gap: 1%;
}

.rider-details .rider-sub-details p {
  color: #555;
}

.food-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.all-food-container img {
  display: block;
  width: 60px;
  height: 60px;
  border-radius: 4px;
  object-fit: cover;
}

.food-container img {
  width: 7rem;
  height: auto;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.icon-container:hover img {
  transform: scale(1.1);
}

.icon-container.active img {
  filter: brightness(1.2) sepia(1) hue-rotate(10deg) saturate(5);
}

.vendor-card:hover .vendor-image img {
  transform: scale(1.05);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.customer-orders-container {
  width: 99%;
  max-width: 1200px;
  margin: 5rem auto;
  padding: 1rem 0.3rem 6rem 0.3rem;
  background-color: #f9f9f9;
  border-radius: 0.75rem;
}

.dashboard-container {
  min-height: calc(100vh - 60px);
  position: relative;
  background-color: #f8f9fa;
}

.dashboard-sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid #e0e0e0;
  background-color: rgba(248, 180, 0, 0.05);
}

.dashboard-logo-icon {
  color: #f8b400;
  font-size: 1.5rem;
  filter: drop-shadow(0 2px 4px rgba(248, 180, 0, 0.3));
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 1.5rem;
}

.dashboard-sidebar.collapsed .dashboard-logo {
  justify-content: center;
}

.dashboard-sidebar-toggle {
  background: #f8b400;
  border: none;
  color: #fff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dashboard-sidebar-toggle:hover {
  background-color: #e5a500;
  color: #fff;
  transform: scale(1.05);
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.1);
  }

  100% {
    transform: scale(1);
  }
}

.dashboard-main {
  flex: 1;
  margin-left: 280px;
  transition: all 0.3s ease;
  width: calc(100% - 280px);
}

.dashboard-main.expanded {
  margin-left: 70px;
  width: calc(100% - 70px);
}

.dashboard-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 6rem;
  height: 0.25rem;
  background-color: #f8b400;
  border-radius: 1rem;
}

.dashboard-header h2 {
  font-size: 2rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.5rem;
}

.dashboard-subtitle {
  font-size: 1.1rem;
  color: #666;
  font-weight: 400;
  padding-top: 0.5rem;
}

/* Empty Orders State */
.empty-orders {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  background-color: #fff;
  border-radius: 0.75rem;
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
  text-align: center;
}

.empty-orders-icon {
  font-size: 3.5rem;
  margin-bottom: 1.5rem;
}

.empty-orders-subtitle {
  font-size: 1rem;
  color: #666;
  max-width: 25rem;
  margin: 0 auto;
}

/* Orders Grid */
.orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

/* Order Card */
.order-card {
  background-color: #fff;
  border-radius: 0.75rem;
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid #eee;
}

.order-card:hover {
  transform: translateY(-0.25rem);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.12);
}

.tutorial-trigger-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: linear-gradient(135deg, #f8b400 0%, #e5a700 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(248, 180, 0, 0.3);
  z-index: 10;
}

/* Order Card Background Colors for Processing Status */
.order-card.status-pending {
  border-top: 0.25rem solid #fff3cd;
}

.order-card.status-confirmed {
  border-top: 0.25rem solid #d1ecf1;
}

.order-card.status-preparing {
  border-top: 0.25rem solid #d4edda;
  background-color: #f9fff9;
}

.order-card.status-ready {
  border-top: 0.25rem solid #cce5ff;
  background-color: #f8fbff;
}

.order-card.status-out-delivery {
  border-top: 0.25rem solid #e2e3ff;
  background-color: #fafbff;
}

.order-card.status-delivered {
  border-top: 0.25rem solid #d4edda;
  background-color: #f9fff9;
}

.order-card.status-received {
  border-top: 0.25rem solid #d4edda;
  background-color: #f9fff9;
}

.order-card.status-delayed {
  border-top: 0.25rem solid #fff3cd;
  background-color: #fffdf5;
}

.order-card.status-not-received {
  border-top: 0.25rem solid #f8d7da;
  background-color: #fff9f9;
}

.order-card.status-cancelled {
  border-top: 0.25rem solid #f8d7da;
  background-color: #fff9f9;
}

/* Order Card Header */
.order-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #eee;
}

.order-id {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.order-label {
  font-size: 0.75rem;
  color: #666;
  margin-bottom: 0.25rem;
}

.order-number {
  font-size: 1.25rem;
  font-weight: 700;
  color: #333;
}

.order-status-badge {
  padding: 0.375rem 0.75rem;
  border-radius: 2rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Order Card Body */
.order-card-body {
  padding: 0.25rem;
}

/* Order Info Section */
.order-info-section {
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #eee;
  padding-bottom: 1rem;
}

.order-info-row {
  display: flex;
  margin-bottom: 0.75rem;
  align-items: flex-start;
}

.info-label {
  width: 40%;
  font-size: 0.875rem;
  color: #666;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-icon {
  color: #f8b400;
}

.info-value {
  width: 60%;
  font-size: 0.875rem;
  color: #333;
  font-weight: 500;
  word-break: break-word;
}

.info-value.price {
  font-weight: 700;
  color: #f8b400;
}

.info-value.address {
  line-height: 1.4;
}

.info-value.rider,
.info-value.vendor {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.rider-name,
.vendor-name {
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.rider-phone,
.vendor-address {
  font-size: 0.8125rem;
  color: #666;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: 0.25rem;
}

.rider-icon,
.vendor-icon {
  color: #666;
}

.info-value.time {
  color: #28a745;
}

/* Order Items Section */
.order-items-section {
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #eee;
  padding-bottom: 1rem;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
  position: relative;
  padding-left: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.section-margin {
  margin-top: 1.5rem;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.25rem;
  bottom: 0.25rem;
  width: 0.25rem;
  background-color: #f8b400;
  border-radius: 1rem;
}

.section-icon {
  color: #f8b400;
  margin-right: 0.25rem;
}

.order-items-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.food-item-card {
  display: flex;
  background-color: #f9f9f9;
  border-radius: 0.5rem;
  overflow: hidden;
  border: 1px solid #eee;
}

.food-item-image {
  width: 30%;
  min-width: 6rem;
  max-width: 8rem;
  overflow: hidden;
  aspect-ratio: 1/1;
}

.food-item-details {
  flex: 1;
  padding: 0.75rem;
  display: flex;
  flex-direction: column;
}

.food-item-name {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.food-item-description {
  font-size: 0.875rem;
  color: #666;
  margin: 0 0 0.75rem 0;
  line-height: 1.4;
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.food-item-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.food-item-price {
  font-weight: 700;
  color: #f8b400;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.price-icon {
  color: #f8b400;
}

.food-item-quantity {
  font-size: 0.75rem;
  color: #666;
  background-color: #f0f0f0;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.quantity-icon {
  color: #666;
}

.no-items-message {
  text-align: center;
  padding: 1.5rem;
  color: #666;
  font-style: italic;
  background-color: #f9f9f9;
  border-radius: 0.5rem;
}

/* Order Actions Section */
.order-actions-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.confirm-delivery-btn {
  background-color: #28a745;
  color: white;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-bottom: 0.75rem;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.confirm-delivery-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.confirm-delivery-btn:hover:not(:disabled) {
  background-color: #218838;
}

.status-update-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.status-label {
  font-size: 0.875rem;
  color: #666;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.label-icon {
  color: #f8b400;
}

.status-select {
  padding: 0.5rem;
  border-radius: 0.375rem;
  border: 1px solid #ddd;
  background-color: #f9f9f9;
  font-size: 0.875rem;
  color: #333;
  font-weight: 500;
  width: 100%;
  cursor: pointer;
  transition: border-color 0.2s;
}

.status-select:focus {
  outline: none;
  border-color: #f8b400;
  box-shadow: 0 0 0 0.125rem rgba(248, 180, 0, 0.25);
}

/* Order Status Styles */
.status-pending {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeeba;
}

.status-confirmed {
  background-color: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.status-preparing {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-ready {
  background-color: #cce5ff;
  color: #004085;
  border: 1px solid #b8daff;
}

.status-out-delivery {
  background-color: #e2e3ff;
  color: #2a2a72;
  border: 1px solid #d4d6ff;
}

.status-delivered {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-received {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-delayed {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeeba;
}

.status-not-received {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.status-cancelled {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.confirm-delivery-section {
  margin: 1rem 0;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 0.5rem;
  border: 1px solid #e0e0e0;
  text-align: center;
}

.delivery-note {
  font-size: 0.875rem;
  color: #6c757d;
  margin-top: 0.5rem;
}

.delivery-confirmation {
  padding: 1rem;
  background-color: white;
  border-radius: 0.5rem;
}

.processing-status {
  display: inline-block;
  margin-left: 0.5rem;
  color: #6c757d;
  font-style: italic;
  font-size: 0.875rem;
}

.payment-status {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background-color: #f8f9fa;
  border-radius: 0.25rem;
  border: 1px solid #e0e0e0;
}

.payment-received {
  color: #28a745;
  font-weight: bold;
}

.payment-pending {
  color: #ffc107;
  font-weight: bold;
}

.profile-container {
  width: 95%;
  max-width: 1000px;
  margin: 5rem auto;
  padding: 0;
  background-color: transparent;
  border-radius: 0.75rem;
  padding-bottom: 5rem;
}

.profile-header {
  background-color: #fff;
  border-radius: 0.75rem 0.75rem 0 0;
  padding: 0.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
  border-bottom: 1px solid #f8b400;
}

.profile-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 0.2rem;
  background-color: #f8b400;
}

.profile-title {
  text-align: center;
  font-size: 1.75rem;
  margin-bottom: 0.5rem;
  color: #333;
  font-weight: 700;
}

.profile-role {
  text-align: center;
  font-size: 1rem;
  color: #f8b400;
  font-weight: 600;
  text-transform: capitalize;
  margin-bottom: 1rem;
}

.profile-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #fff;
  border-radius: 0 0 0.75rem 0.75rem;
  padding: 0.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.08);
}

.avatar-container {
  width: 8rem;
  height: 8rem;
  margin-bottom: 2rem;
  border-radius: 10%;
  overflow: hidden;
  border: 1px solid #f8b400;
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
  position: relative;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-upload {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  text-align: center;
  padding: 0.25rem 0;
  font-size: 0.75rem;
  cursor: pointer;
  border: none;
  transition: background-color 0.2s ease;
}

.avatar-upload:hover,
.avatar-upload:focus {
  background-color: rgba(0, 0, 0, 0.8);
  outline: none;
}

.profile-form {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.profile-section {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 1rem;
  margin-top: 1.5rem;
}

.profile-section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #f8b400;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
}

.profile-section-title::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 3rem;
  height: 2px;
  background-color: #f8b400;
}

.profile-fields-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.profile-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.profile-field-label {
  font-size: 0.9rem;
  color: #666;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.profile-field-icon {
  color: #f8b400;
  margin-right: 0.5rem;
}

.profile-form input[type="text"],
.profile-form input[type="email"],
.profile-form input[type="date"],
.profile-form select {
  width: 100%;
  padding: 0.75rem;
  font-size: 0.9rem;
  border: 1px solid #ddd;
  border-radius: 0.5rem;
  background-color: #f9f9f9;
  transition: all 0.2s ease;
}

.profile-form select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f8b400' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1rem;
  padding-right: 2.5rem;
}

.profile-form input[type="text"]:focus,
.profile-form input[type="email"]:focus,
.profile-form input[type="date"]:focus,
.profile-form select:focus {
  outline: none;
  border-color: #f8b400;
  box-shadow: 0 0 0 2px rgba(248, 180, 0, 0.2);
  background-color: #fff;
}

.profile-form input[type="file"] {
  width: 100%;
  padding: 0.5rem;
  font-size: 0.9rem;
  border: 1px dashed #ddd;
  border-radius: 0.5rem;
  background-color: #f9f9f9;
  cursor: pointer;
}

.profile-form input[type="file"]:hover {
  background-color: #f5f5f5;
}

/* Document Upload Styling */
.document-upload-container {
  border: 1px dashed #ddd;
  border-radius: 0.5rem;
  padding: 1rem;
  background-color: #f9f9f9;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.document-upload-container:hover {
  background-color: #f5f5f5;
  border-color: #f8b400;
}

.document-upload-icon {
  color: #f8b400;
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.document-upload-text {
  font-size: 0.9rem;
  color: #666;
}

/* Mobile Money Section */
.mobile-money-container {
  background-color: #fff8e6;
  border-radius: 0.5rem;
  padding: 1rem;
  border-left: 3px solid #f8b400;
  margin-bottom: 1.5rem;
}

.mobile-money-title {
  font-size: 1rem;
  font-weight: 600;
  color: #f8b400;
  margin-bottom: 1rem;
}

button[type="submit"] {
  align-self: center;
  padding: 0.8rem 2rem;
  font-size: 1.2rem;
  font-weight: 600;
  color: #fff;
  background-color: #007bff;
  border: none;
  border-radius: 0.4rem;
  cursor: pointer;
}

button[type="submit"]:hover {
  background-color: #0056b3;
}

.data-container,
.all-food-container {
  width: 98%;
  margin: 1rem auto;
  padding: 0.5rem 0.5rem 7rem 0.5rem;
  background-color: #f8f8f8;
  border-radius: 1.5rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
}

.data-container {
  padding: 0.5rem 0 7rem 0;
}

.data-para-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  gap: 0.3rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.food-container h5 {
  font-size: 1rem;
  color: #555;
  font-weight: 700;
  line-height: 1.3;
}

.food-container,
.all-food-container {
  display: flex;
  flex-direction: column;
}

.all-food-container {
  border-collapse: collapse;
  border: 1px solid #ddd;
}

.all-food-container caption {
  font-size: 1.5em;
  margin-bottom: 0.7rem;
  font-weight: bold;
  color: #000;
}

.all-food-container thead {
  display: table;
}

.all-food-container th,
.all-food-container td {
  padding: 0.3rem;
  border: 1px solid #ddd;
  text-align: left;
  color: #000;
}

.all-food-container th {
  background-color: #f2f2f2;
}

.all-food-container tr:nth-child(even) {
  background-color: #f9f9f9;
}

.food-container li {
  display: flex;
  align-items: center;
}

.add-food-form {
  width: 100%;
  margin: 1rem auto;
  padding: 0.5rem 0.3rem 1rem 0.3rem;
  border: 0.1rem solid #ddd;
  border-radius: 1.5rem;
  background-color: #f8f8f8;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
}

.add-food-form div {
  margin-bottom: 1.5rem;
}

.greetings-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

.greetings-wrapper h3 {
  font-size: 1.5rem;
  color: #000;
}

.orders-table {
  width: 100%;
  margin: 1rem 0;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid #eee;
}

.detail-label {
  font-weight: 600;
  color: #666;
  margin-right: 1rem;
}

.detail-value {
  color: #333;
  text-align: right;
}

.ordered-foods {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 2px solid #eee;
}

.ordered-foods h4 {
  color: #000;
  text-align: center;
  text-decoration: underline;
}

.food-item {
  display: flex;
  align-items: center;
  padding: 0.3rem;
  background: #f8f9fa;
  border-radius: 6px;
  border-bottom: 1px solid #eee;
}

.food-img {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  object-fit: cover;
  margin-right: 1rem;
}

.food-details {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  flex-direction: row;
  color: #eee;
}

.vendor-food-name {
  font-weight: 500;
  margin-bottom: 0.3rem;
  color: #333;
}

.vendor-food-price {
  color: #28a745;
  font-size: 0.9rem;
}

.food-quantity {
  color: #666;
  font-size: 0.85rem;
}

.payment-card {
  background-color: #fff;
  border: 0.0625rem solid #e0e0e0;
  border-radius: 0.75rem;
  padding: 0.5rem;
  max-width: 25rem;
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
}

.payment-summary {
  margin-bottom: 1rem;
}

.payment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  color: #555;
}

.payment-item-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.payment-item-value {
  font-weight: 500;
  color: #333;
}

.payment-divider {
  height: 1px;
  background-color: #e0e0e0;
  margin: 0.5rem 0;
}

.total-item {
  font-weight: 600;
  color: #333;
  font-size: 1.1rem;
}

.total-value {
  color: #f8b400;
  font-weight: 700;
  font-size: 1.2rem;
}

.donation-item {
  color: #28a745;
  background-color: rgba(40, 167, 69, 0.05);
  border-radius: 0.5rem;
  padding: 0.5rem;
  margin: 0.5rem 0;
}

.payment-card label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 1rem 0;
  font-size: 0.9rem;
  color: #555;
  cursor: pointer;
}

.payment-button {
  width: 100%;
  padding: 0.75rem;
  background-color: #f8b400;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.2s ease;
  margin-top: 0.5rem;
}

.payment-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.payment-button:hover:not(:disabled) {
  background-color: #e5a700;
  transform: translateY(-0.125rem);
}

.section {
  margin: 0.75rem 0;
  border: 0.0625rem solid #f0f0f0;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.05);
  background-color: #fff;
}

.section-header {
  margin-bottom: 1.5rem;
  padding: 0 0.5rem;
}

.section .section-header,
.no-outside-dunkwa {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #333;
  background-color: #fff;
  padding: 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 0.0625rem solid #f0f0f0;
}

.section .section-header:hover {
  background-color: #f9f9f9;
}

.contact-details h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.25rem;
  color: #333;
}

.quick-link-content h4 {
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 0.25rem;
}

.vendors-confirmed-notice h4 {
  color: #28a745;
}

.vendors-status-notice h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-top: 0;
  margin-bottom: 0.5rem;
}

.guide-header svg {
  margin-right: 0.75rem;
}

.category-item svg {
  margin-right: 0.3rem;
  color: inherit;
}

.no-results svg {
  color: #f8b400;
  margin-bottom: 1rem;
}

.section .section-header h4 svg {
  color: #4caf50;
  margin-left: 0.5rem;
  flex-shrink: 0;
}

.section-content {
  padding: 0.3rem;
  background-color: #fff;
}

.toggle-container {
  border-radius: 0.4em;
  background-color: #f5f5f5;
}

.toggle-container label {
  display: flex;
  align-items: center;
  gap: 0.5em;
  cursor: pointer;
  font-size: 0.9em;
  color: #333;
}

.toggle-container input[type="checkbox"] {
  appearance: none;
  width: 1em;
  height: 1em;
  padding: 0.4rem;
  border: 0.15em solid #ccc;
  border-radius: 0.3em;
  cursor: pointer;
  position: relative;
  transition:
    background-color 0.2s ease,
    border-color 0.2s ease;
}

.toggle-container input[type="checkbox"]:checked {
  background-color: #007bff;
  border-color: #007bff;
}

.toggle-container input[type="checkbox"]:checked::after {
  content: "✓";
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 0.9em;
}

.toggle-container input[type="checkbox"]:focus {
  outline: 0.15em solid rgba(0, 123, 255, 0.3);
  outline-offset: 0.1em;
}

.order-status-notice {
  padding: 0.4rem;
  border-radius: 0.5rem;
  font-size: 1rem;
  line-height: 1.5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pending-notice {
  background-color: #fff3cd;
  border-left: 5px solid #ffc107;
  color: #856404;
}

.confirmed-notice {
  background-color: #d4edda;
  border-left: 5px solid #28a745;
  color: #155724;
}

.order-status-notice h4 {
  margin-top: 0;
  margin-bottom: 0.3rem;
  font-size: 1rem;
  font-weight: 600;
}

.ready-notice {
  background-color: rgba(76, 175, 80, 0.1) !important;
  border-left: 0.25rem solid #4caf50 !important;
}

.ready-notice h4 {
  color: #4caf50 !important;
}

.rider-assigned-badge {
  display: inline-block;
  background-color: #4caf50;
  color: white;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  margin-left: 0.5rem;
  font-weight: bold;
}

.rider-info {
  margin-top: 0.5rem;
  padding: 0.75rem;
  background-color: rgba(248, 180, 0, 0.1);
  border-left: 0.25rem solid #f8b400;
  border-radius: 0.25rem;
}

.rider-info .detail-row {
  margin-bottom: 0.5rem;
}

.rider-info .detail-label {
  font-weight: bold;
  color: #333;
}

.rider-info .detail-value {
  color: #555;
}

.no-rider-badge {
  display: inline-block;
  background-color: #f44336;
  color: white;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  margin-left: 0.5rem;
  font-weight: bold;
}

/* Status Change Confirmation Styles */
.status-change-confirmation {
  padding: 0.9375rem;
  background-color: #2a2b2a;
  border-radius: 0.5rem;
  border-left: 0.25rem solid #5bc0de;
}

.confirmation-buttons {
  display: flex;
  gap: 0.625rem;
  justify-content: flex-end;
  margin-top: 1rem;
}

.confirm-btn,
.cancel-btn {
  border: none;
  border-radius: 0.25rem;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.3s ease;
}

.confirm-btn {
  background-color: #5cb85c;
  color: #fff;
}

.confirm-btn:hover {
  background-color: #4cae4c;
}

.cancel-btn {
  background-color: #d9534f;
  color: #fff;
}

.cancel-btn:hover {
  background-color: #c9302c;
}

/* Payouts Table Styles */
.payouts-container {
  padding: 1.25rem;
  border-radius: 0.5rem;
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.2);
}

.payouts-list h3 {
  color: #f8b400;
  text-align: center;
  margin-bottom: 1.25rem;
}

.payouts-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 0.9375rem;
}

.payouts-table th,
.payouts-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 0.0625rem solid #3a3b3a;
}

.payouts-table th {
  background-color: #1c1d1c;
  color: #f8b400;
  font-weight: bold;
}

.payout-row {
  transition: background-color 0.3s ease;
}

.payout-row:hover {
  background-color: #3a3b3a;
}

.payout-row.completed {
  background-color: rgba(92, 184, 92, 0.1);
}

.payout-row.pending {
  background-color: rgba(240, 173, 78, 0.1);
}

.payout-row.failed {
  background-color: rgba(217, 83, 79, 0.1);
}

/* VendorsDashboard Styles */
.vendors-dashboard-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 0.5rem 6rem;
}

/* Dashboard Header */
.vendors-dashboard-header {
  position: relative;
  width: 100%;
  margin-bottom: 0.5rem;
}

.vendors-dashboard-banner {
  height: 180px;
  position: relative;
}

/* Dashboard Title Section */
.vendors-dashboard-title-section {
  text-align: center;
  padding: 1.5rem 1rem;
  margin-top: 3.9rem;
  margin-bottom: 1rem;
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
  position: relative;
}

.vendors-dashboard-title-section h1 {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #333;
}

/* Dashboard Navigation */
.vendors-dashboard-nav {
  background-color: #f8f9fa;
  border-radius: 0.75rem;
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  padding: 0.5rem 0.5rem 0;
}

.vendors-dashboard-nav::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.vendors-nav-tabs {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 1rem 0;
}

.vendors-nav-tab {
  padding: 1rem 1.25rem;
  white-space: nowrap;
  color: #666;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  border: none;
  background: none;
  font-size: 0.9rem;
  margin: 0 0.25rem;
}

.vendors-nav-tab:hover {
  color: #f8b400;
}

.vendors-nav-tab.active {
  color: #f8b400;
  background-color: white;
  border-radius: 0.5rem 0.5rem 0 0;
  box-shadow: 0 -0.25rem 0.5rem rgba(0, 0, 0, 0.05);
}

.vendors-nav-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #f8b400;
}

/* Dashboard Content */
.vendors-dashboard-content {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
  padding: 0.5rem;
  margin-bottom: 1.5rem;
}

.vendors-section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.vendors-icon {
  color: #f8b400;
}

/* Status Notices */
.vendors-status-notice {
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
}

.vendors-pending-notice {
  background-color: #fff8e6;
  border-left: 4px solid #f8b400;
}

.vendors-pending-notice h4 {
  color: #f8b400;
}

.vendors-confirmed-notice {
  background-color: #e6f7ef;
  border-left: 4px solid #28a745;
}

.vendors-ready-notice {
  background-color: #e6f4fa;
  border-left: 4px solid #0d6efd;
}

.vendors-ready-notice h4 {
  color: #0d6efd;
}

.vendors-error-notice {
  background-color: #fff5f5;
  border-left: 4px solid #dc3545;
}

.vendors-error-notice h4 {
  color: #dc3545;
}

.complaint-response h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.vendors-ordered-foods h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.75rem;
  text-align: center;
}

.section .section-header h4 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #444;
}

.vendors-loading {
  text-align: center;
  padding: 2rem;
  color: #666;
  font-size: 1rem;
}

/* Order Cards */
.vendors-orders-container {
  margin-top: 1.5rem;
}

.vendors-orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.vendors-order-card {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1px solid #f0f0f0;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.vendors-order-card:hover {
  transform: translateY(-0.25rem);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.12);
}

.vendors-order-header {
  background-color: #f8f9fa;
  padding: 1rem;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.vendors-order-id {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.vendors-order-time {
  font-size: 0.8rem;
  color: #666;
}

.vendors-order-details {
  padding: 0.5rem;
}

.vendors-detail-row {
  display: flex;
  margin-bottom: 0.75rem;
  align-items: center;
}

.vendors-detail-label {
  font-weight: 600;
  color: #666;
  width: 40%;
  font-size: 0.9rem;
}

.vendors-detail-value {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #333;
  flex: 1;
  font-size: 0.9rem;
  text-align: left;
}

/* Status value styling */
.vendors-status-value {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  text-transform: capitalize;
  background-color: #f8f9fa;
  border-left: 3px solid #666;
}

.vendors-status-value.pending {
  background-color: #fff8e6;
  border-left-color: #f8b400;
  color: #f8b400;
}

.vendors-status-value.confirmed {
  background-color: #e6f7ef;
  border-left-color: #28a745;
  color: #28a745;
}

.vendors-status-value.processing {
  background-color: #e6f4fa;
  border-left-color: #0d6efd;
  color: #0d6efd;
}

.vendors-status-value.ready_for_pickup {
  background-color: #e6f4fa;
  border-left-color: #17a2b8;
  color: #17a2b8;
}

.vendors-status-value.delivered {
  background-color: #e6f7ef;
  border-left-color: #28a745;
  color: #28a745;
}

.vendors-status-value.cancelled {
  background-color: #fff5f5;
  border-left-color: #dc3545;
  color: #dc3545;
}

.vendors-rider-info {
  background-color: #f8f9fa;
  border-radius: 0.5rem;
  padding: 0.5rem;
  margin: 0.5rem 0;
  border-left: 3px solid #f8b400;
}

.vendors-rider-name {
  vertical-align: middle;
}

.vendors-rider-assigned-badge {
  display: inline-block;
  background-color: #28a745;
  color: white;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  vertical-align: middle;
}

.vendors-payment-received {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 0.5rem;
  background-color: #e6f7ef;
  border-left: 3px solid #28a745;
  color: #28a745;
  font-weight: 600;
}

.vendors-payment-pending {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 0.5rem;
  background-color: #fff5f5;
  border-left: 3px solid #dc3545;
  color: #dc3545;
  font-weight: 600;
}

.vendors-status-select {
  width: 100%;
  padding: 0.5rem;
  border-radius: 0.5rem;
  border: 1px solid #ddd;
  background-color: white;
  color: #333;
  font-size: 0.9rem;
  margin-top: 0.25rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.vendors-status-select:focus {
  outline: none;
  border-color: #f8b400;
  box-shadow: 0 0 0 2px rgba(248, 180, 0, 0.2);
}

.vendors-status-select option {
  padding: 0.5rem;
}

.vendors-ordered-foods {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #f0f0f0;
}

.vendors-food-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  background-color: #f8f9fa;
  border-radius: 0.5rem;
  margin-bottom: 0.5rem;
}

.vendors-food-img {
  width: 85px;
  height: 85px;
  border-radius: 0.25rem;
  object-fit: cover;
  margin-right: 1rem;
}

.vendors-food-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.vendors-food-name {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.vendors-food-meta {
  display: flex;
  gap: 1rem;
}

.vendors-food-price,
.vendors-food-quantity {
  font-size: 0.8rem;
  color: #666;
  background-color: #fff;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  display: inline-flex;
  align-items: center;
}

.vendors-food-description {
  margin-top: 0.25rem;
  margin-bottom: 0.5rem;
  font-size: 0.85rem;
  color: #555;
  line-height: 1.4;
}

.vendors-description-toggle {
  background: none;
  border: none;
  color: #f8b400;
  font-size: 0.8rem;
  padding: 0.25rem 0;
  cursor: pointer;
  text-decoration: underline;
  margin-top: 0.25rem;
  display: block;
}

.vendors-description-toggle:hover {
  color: #e5a400;
}

.vendors-food-price {
  color: #28a745;
}

/* Payouts Table */
.vendors-payouts-container {
  margin-top: 1.5rem;
}

.vendors-payouts-table-wrapper {
  overflow-x: auto;
}

.vendors-payouts-table {
  width: 100%;
  border-collapse: collapse;
}

.vendors-payouts-table th,
.vendors-payouts-table td {
  padding: 0.5rem;
  text-align: left;
  border-bottom: 1px solid #f0f0f0;
}

.vendors-payouts-table th {
  font-weight: 600;
  color: #666;
  background-color: #f8f9fa;
}

.vendors-payouts-table tr:hover {
  background-color: #f8f9fa;
}

.vendors-payout-status {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
}

.vendors-status-completed {
  background-color: #e6f7ef;
  color: #28a745;
}

.vendors-status-pending {
  background-color: #fff8e6;
  color: #f8b400;
}

/* Foods Table */
.vendors-foods-table-container {
  overflow-x: auto;
}

.vendors-foods-table {
  width: 100%;
  border-collapse: collapse;
}

.vendors-foods-table th,
.vendors-foods-table td {
  padding: 0.5rem;
  text-align: left;
  border-bottom: 1px solid #f0f0f0;
}

.vendors-foods-table th {
  font-weight: 600;
  color: #666;
  background-color: #f8f9fa;
}


/* Redesigned Vendors Foods Listing */
.vendors-foods-table-container {
  overflow-x: auto;
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 0.75rem;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.06);
}

.vendors-foods-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.vendors-foods-table thead th {
  background: #fafafa;
  color: #555;
  font-weight: 700;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.03em;
  border-bottom: 1px solid #eee;
  padding: 0.85rem;
}

.vendors-foods-table tbody td {
  padding: 0.85rem;
  vertical-align: middle;
  border-bottom: 1px solid #f5f5f5;
  color: #333;
}

.vendors-foods-table tbody tr:hover {
  background: #fffdf6;
}

.vendors-foods-table-img {
  width: 52px;
  height: 52px;
  border-radius: 10px;
  object-fit: cover;
  border: 1px solid #f0f0f0;
}

/* Name & description clamp for neat rows */
.vendors-foods-name { font-weight: 700; color: #040505; }
.vendors-foods-desc { color: #666; font-size: 0.9rem; max-width: 520px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; }

/* Price pill */
.vendors-foods-price {
  display: inline-flex;
  align-items: center;
  gap: 0.35rem;
  background: #fff8e6;
  color: #f8b400;
  border: 1px solid rgba(248, 180, 0, 0.25);
  padding: 0.25rem 0.5rem;
  border-radius: 0.5rem;
  font-weight: 700;
}

/* Actions cell */
.vendors-actions-cell { white-space: nowrap; }

.vendors-food-table-img {
  width: 60px;
  height: 60px;
  border-radius: 0.25rem;
  object-fit: cover;
}

/* Add Food Form */
.vendors-add-food-container {
  max-width: 600px;
  margin: 0 auto;
}

/* Price Notice Styling */
#price-notice {
  background-color: #fff8e6;
  border-left: 5px solid #f8b400;
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 0.5rem;
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.vendors-price-note {
  display: block;
  font-size: 0.75rem;
  color: #666;
  margin-top: 0.25rem;
  font-style: italic;
  background-color: #fff8e6;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  border-left: 2px solid #f8b400;
}

.vendors-full-address {
  white-space: normal !important;
  overflow: visible !important;
  text-overflow: clip !important;
  word-break: break-word;
}

#price-notice strong {
  color: #f8b400;
  font-size: 1.1rem;
  display: block;
  margin-bottom: 0.5rem;
}

.icon-container:hover p {
  color: #f8b400;
}

#price-notice p {
  margin: 0.5rem 0 0 0;
  font-size: 0.9rem;
  color: #666;
}

/* Empty State */
.vendors-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
}

.vendors-empty-state-icon {
  color: #ddd;
  margin-bottom: 1rem;
}

/* Riders Section Heading Styles */
.riders-section-heading {
  font-size: 0.9rem;
  font-weight: 600;
  color: #f8b400;
  margin: 0.75rem 0 0.5rem;
  padding-bottom: 0.25rem;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
}

.riders-section-heading:first-child {
  margin-top: 0;
}

.riders-section-heading::before {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 2rem;
  height: 2px;
  background-color: #f8b400;
}

.contact-page {
  width: 100%;
  max-width: 75rem;
  margin: 5rem auto;
  padding: 0.5rem 0.5rem 7rem;
  color: #333;
}

.contact-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.contact-header h1 {
  font-size: 2rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.75rem;
}

.contact-container {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
}

.contact-info {
  flex: 1;
  min-width: 18rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.contact-card {
  background-color: white;
  border-radius: 0.75rem;
  padding: 0.5rem;
  box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.contact-card:hover {
  transform: translateY(-0.25rem);
  box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.15);
}

.contact-card h2 {
  display: flex;
  align-items: center;
  font-size: 1.25rem;
  font-weight: 600;
  color: #f8b400;
  margin-bottom: 1rem;
}

.contact-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.contact-list li {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  font-size: 0.95rem;
  color: #444;
}

.list-icon {
  margin-right: 0.5rem;
  flex-shrink: 0;
}

.contact-form-container {
  flex: 1;
  background-color: #f9f9f9;
  border-radius: 0.75rem;
  padding: 0.5rem;
  box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.1);
}

.contact-form-container h2 {
  display: flex;
  align-items: center;
  font-size: 1.25rem;
  font-weight: 600;
  color: #f8b400;
  margin-bottom: 1.25rem;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
}

.form-group label {
  display: flex;
  flex-direction: column;
  font-size: 0.9rem;
  font-weight: 500;
  color: #444 !important;
}

.form-icon {
  margin-right: 0.5rem;
  color: #444 !important;
}

.form-group label > span.required {
  margin-left: 0.25rem;
  color: #f8b400;
  display: inline;
}

.form-group label > div {
  display: flex;
  margin-bottom: 0.5rem;
}

.contact-form textarea {
  resize: vertical;
  min-height: 8rem;
  background-color: #f9f9f9;
}

.contact-form input:focus,
.contact-form textarea:focus {
  border-color: #f8b400;
  box-shadow: 0 0 0 0.125rem rgba(248, 180, 0, 0.2);
  outline: none;
  background-color: #fff;
}

.contact-form input::placeholder,
.contact-form textarea::placeholder {
  color: rgba(0, 0, 0, 0.4);
}

.submit-button:hover {
  background-color: #e5a700;
  transform: translateY(-0.125rem);
}

.submit-button:active {
  transform: translateY(0);
}

.no-links li {
  padding-left: 1.5rem;
}

.contact-divider {
  height: 0.0625rem;
  background-color: rgba(0, 0, 0, 0.1);
  margin: 0.75rem 0;
}

.suggestions-page {
  width: 100%;
  max-width: 75rem;
  margin: 5rem auto;
  padding: 0.5rem 0.5rem 7rem;
  color: #333;
}

.suggestions-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.suggestions-header h1 {
  font-size: 2rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.75rem;
}

.suggestions-form-container {
  background-color: #f9f9f9;
  border-radius: 0.75rem;
  padding: 0.5rem;
  box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.1);
}

.suggestions-form-container h2 {
  display: flex;
  align-items: center;
  font-size: 1.25rem;
  font-weight: 600;
  color: #f8b400;
  margin-bottom: 1.25rem;
}

.suggestions-icon {
  margin-right: 0.75rem;
  color: #f8b400;
}

.suggestions-form {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.form-select {
  padding: 0.75rem 1rem;
  border: 0.0625rem solid #e0e0e0;
  border-bottom: 0.125rem solid #f8b400;
  border-radius: 0.5rem;
  font-size: 1rem;
  background-color: #fff;
  color: #333;
  width: 100%;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23f8b400' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1rem;
}

.form-select:focus {
  border-color: #f8b400;
  box-shadow: 0 0 0 0.125rem rgba(248, 180, 0, 0.2);
  outline: none;
  transform: translateY(-2px);
}

.rating-container {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.rating-star {
  background: none;
  border: none;
  cursor: pointer;
  color: #ccc;
  transition: color 0.2s ease;
  padding: 0;
}

.rating-star.active {
  color: #f8b400;
}

.rating-star:hover {
  transform: scale(1.1);
}

.submitted-suggestions {
  margin-top: 0.8rem;
}

.submitted-suggestions h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1.25rem;
  padding-bottom: 0.5rem;
  border-bottom: 0.0625rem solid #e0e0e0;
}

.no-suggestions {
  padding: 2rem;
  text-align: center;
  background-color: #f9f9f9;
  border-radius: 0.75rem;
  color: #666;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.suggestion-card {
  background-color: #fff;
  border-radius: 0.75rem;
  padding: 1.25rem;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.05);
  border-left: 0.25rem solid #f8b400;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.desktop-menu-container {
  display: none;
}

.suggestion-card:hover {
  transform: translateY(-0.125rem);
  box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.1);
}

.suggestion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.suggestion-category {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #555;
}

.suggestion-status {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  text-transform: uppercase;
}

.status-implemented {
  background-color: #e6f7e6;
  color: #28a745;
}

.suggestion-content {
  margin-bottom: 1rem;
  line-height: 1.5;
  color: #333;
}

.suggestion-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
  color: #777;
  border-top: 0.0625rem solid #f0f0f0;
  padding-top: 0.75rem;
}

.suggestion-user,
.suggestion-date,
.suggestion-rating {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.suggestion-rating .filled {
  color: #f8b400;
}

.complaints-page {
  width: 100%;
  max-width: 75rem;
  margin: 5rem auto;
  padding: 0.5rem 0.5rem 7rem;
  color: #333;
}

.complaints-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.complaints-header h1 {
  font-size: 2rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.75rem;
}

.complaints-container {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
}

.complaints-form-container {
  flex: 1;
  background-color: #f9f9f9;
  border-radius: 0.75rem;
  padding: 0.5rem;
  box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.1);
}

.complaints-form-container.full-width {
  max-width: 50rem;
  margin: 0 auto;
  width: 100%;
}

.complaints-form-container h2 {
  display: flex;
  align-items: center;
  font-size: 1.25rem;
  font-weight: 600;
  color: #f8b400;
  margin-bottom: 1.25rem;
}

.complaints-icon {
  margin-right: 0.75rem;
  color: #f8b400;
}

.complaints-form {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.file-upload-container {
  flex-direction: column !important;
  align-items: flex-start !important;
}

.file-upload-info {
  margin-top: 0.5rem;
  font-size: 0.8rem;
  color: #666;
}

.file-list {
  margin-top: 0.5rem;
  list-style: none;
  padding: 0;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
  color: #333;
  margin-bottom: 0.25rem;
  background-color: #f0f0f0;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

.submitted-complaints {
  flex: 1;
}

.submitted-complaints h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1.25rem;
  padding-bottom: 0.5rem;
  border-bottom: 0.0625rem solid #e0e0e0;
}

.no-complaints {
  padding: 2rem;
  text-align: center;
  background-color: #f9f9f9;
  border-radius: 0.75rem;
  color: #666;
}

.complaints-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.complaint-card {
  background-color: #fff;
  border-radius: 0.75rem;
  padding: 1.25rem;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.05);
  border-left: 0.25rem solid #f8b400;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.complaint-card:hover {
  transform: translateY(-0.125rem);
  box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.1);
}

.complaint-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.complaint-category {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #555;
}

.complaint-status {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  text-transform: uppercase;
}

.status-review {
  background-color: #fff4de;
  color: #f8b400;
}

.status-resolved {
  background-color: #e6f7e6;
  color: #28a745;
}

.complaint-order-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #555;
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 0.0625rem solid #f0f0f0;
}

.complaint-content {
  margin-bottom: 1rem;
  line-height: 1.5;
  color: #333;
}

.complaint-response {
  background-color: #f9f9f9;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
}

.complaint-response h4::before {
  content: '';
  display: inline-block;
  width: 0.5rem;
  height: 0.5rem;
  background-color: #28a745;
  border-radius: 50%;
}

.complaint-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
  color: #777;
  border-top: 0.0625rem solid #f0f0f0;
  padding-top: 0.75rem;
}

.complaint-user,
.complaint-date,
.complaint-attachments {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Support Page Styles */
.support-page {
  width: 100%;
  max-width: 75rem;
  margin: 5rem auto;
  padding: 1.5rem 1rem 7rem;
  color: #333;
}

.support-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.support-header h1 {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.75rem;
}

.support-icon {
  margin-right: 0.75rem;
  color: #f8b400;
}

.support-search {
  position: relative;
  max-width: 40rem;
  margin: 0 auto;
}

.search-input {
  width: 100%;
  padding: 0.5rem !important;
  border: 0.0625rem solid #e0e0e0;
  border-radius: 1.5rem !important;
  font-size: 0.85rem !important;
  text-align: center;
  background-color: #fff;
  transition: all 0.2s ease;
}

.search-input:focus {
  border-color: #f8b400;
  box-shadow: 0 0 0 0.125rem rgba(248, 180, 0, 0.2);
  outline: none;
}

.clear-search {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #666;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.support-main {
  flex: 3;
}

.categories-container,
.quick-links-container {
  background-color: #fff;
  border-radius: 0.75rem;
  padding: 1rem;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.05);
  margin-bottom: 1.5rem;
}

.categories-container h3,
.quick-links-container h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 0.0625rem solid #f0f0f0;
}

.categories-list {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.2rem;
}

.categories-list li {
  width: 40%;
  flex: 1;
}

.category-item {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 0.3rem;
}

.category-item:hover {
  background-color: #f9f9f9;
}

.category-item.active {
  background-color: #fff4de;
  color: #f8b400;
}

.quick-links {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.quick-link-card {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border-radius: 0.5rem;
  background-color: #f9f9f9;
  text-decoration: none;
  color: #333;
  transition: all 0.2s ease;
}

.quick-link-card:hover {
  transform: translateY(-0.125rem);
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
}

.quick-link-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.5rem;
  margin-right: 0.75rem;
  color: #fff;
}

.faqs-container,
.navigation-guides,
.contact-support {
  background-color: #fff;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
}

.faqs-container h2,
.navigation-guides h2,
.contact-support h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1.5rem;
}

.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
  text-align: center;
  color: #666;
}

.reset-search {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: #f8b400;
  color: #fff;
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.reset-search:hover {
  background-color: #e5a700;
}

.faqs-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.faq-item {
  border: 0.0625rem solid #f0f0f0;
  border-radius: 0.5rem;
  overflow: hidden;
  transition: all 0.3s ease;
}

.faq-item.expanded {
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 1rem 1.5rem;
  cursor: pointer;
  background-color: #f9f9f9;
  transition: background-color 0.2s ease;
}

.faq-item.expanded .faq-question {
  background-color: #fff4de;
}

.faq-question h3 {
  font-size: 1rem;
  font-weight: 500;
  text-align: left;
  margin: 0;
  flex: 1;
}

.faq-icon {
  color: #f8b400;
}

.faq-answer {
  padding: 1rem 1.5rem;
  background-color: #fff;
  border-top: 0.0625rem solid #f0f0f0;
}

.guides-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(18rem, 1fr));
  gap: 1.5rem;
}

.guide-card {
  background-color: #f9f9f9;
  border-radius: 0.5rem;
  padding: 1.25rem;
  transition: all 0.2s ease;
}

.guide-card:hover {
  transform: translateY(-0.125rem);
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
}

.guide-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  color: #f8b400;
}

.guide-header h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  color: #333;
}

.guide-steps {
  padding-left: 1.5rem;
  margin: 0;
}

.guide-steps li {
  margin-bottom: 0.5rem;
  color: #555;
  line-height: 1.5;
}

.guide-steps li:last-child {
  margin-bottom: 0;
}

.support-contact-options {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
}

.support-contact-card {
  display: flex;
  align-items: center;
  padding: 1rem;
  background-color: #f9f9f9;
  border-radius: 0.5rem;
  flex: 1;
  min-width: 15rem;
}

.contact-icon {
  margin-right: 1rem;
  color: #f8b400;
}

.contact-button {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background-color: #f8b400;
  color: #fff;
  text-decoration: none;
  border-radius: 0.25rem;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.contact-button:hover {
  background-color: #e5a700;
}

.input-icon {
  font-size: 1rem;
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #aaa;
}

.categories-management {
  width: 100%;
}

.category-form {
  background-color: var(--bg-light);
  border-radius: var(--border-radius-md);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: var(--shadow-sm);
  border-left: 4px solid var(--primary-color);
}

.category-form h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: var(--text-primary);
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

.save-button,
.cancel-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-sm);
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: var(--transition);
}

.save-button {
  background-color: var(--success-color);
  color: white;
}

.cancel-button {
  background-color: var(--text-light);
  color: var(--text-primary);
}

.save-button:hover {
  background-color: var(--success-dark);
}

.cancel-button:hover {
  background-color: var(--text-secondary);
  color: white;
}

.edit-input,
.edit-textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: 0.875rem;
  transition: var(--transition);
}

.edit-input:focus,
.edit-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(248, 180, 0, 0.2);
}

/* Document Modal Styles */
.document-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1100;
  padding: 1rem;
  animation: fadeIn 0.2s ease-out;
}

.document-modal-content {
  background-color: var(--bg-white);
  border-radius: var(--border-radius-md);
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: var(--shadow-lg);
  animation: scaleIn 0.2s ease-out;
}

.document-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.document-modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.document-modal-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1.25rem;
  cursor: pointer;
  transition: var(--transition);
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.document-modal-close:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--danger-color);
}

.document-modal-body {
  padding: 1.5rem;
  overflow-y: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }

  to {
    transform: scale(1);
    opacity: 1;
  }
}

@media (max-width: 480px) {
  .support-page {
    padding: 1rem 0.75rem 7rem;
  }

  .support-header {
    margin-bottom: 1.5rem;
  }

  .support-header h1 {
    font-size: 1.5rem;
  }

  .support-header p {
    font-size: 0.9rem;
  }

  .faqs-container,
  .navigation-guides,
  .contact-support {
    padding: 1.25rem;
  }

  .faqs-container h2,
  .navigation-guides h2,
  .contact-support h2 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
  }

  .faq-question {
    padding: 0.75rem 1rem;
  }

  .faq-question h3 {
    font-size: 0.9rem;
  }

  .faq-answer {
    padding: 0.75rem 1rem;
  }

  .guide-card {
    padding: 1rem;
  }

  .guide-header h3 {
    font-size: 1rem;
  }

  .complaints-header {
    margin-bottom: 1.5rem;
  }

  .complaints-header h1 {
    font-size: 1.5rem;
  }

  .complaints-header p {
    font-size: 0.9rem;
  }

  .complaint-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .suggestions-header {
    margin-bottom: 1.5rem;
  }

  .suggestions-header h1 {
    font-size: 1.5rem;
  }

  .suggestions-header p {
    font-size: 0.9rem;
  }

  .suggestion-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .contact-header {
    margin-bottom: 1.5rem;
  }

  .contact-header h1 {
    font-size: 1.5rem;
  }

  .contact-header p {
    font-size: 0.9rem;
  }

  .vendors-dashboard-banner {
    height: 150px;
  }

  .vendors-dashboard-title-section {
    padding: 1rem;
  }

  .vendors-dashboard-title-section h1 {
    font-size: 1.25rem;
  }

  .vendors-section-title {
    font-size: 1.1rem;
  }

  .vendors-food-img {
    width: 85px;
    height: 85px;
  }

  .dashboard-header h2 {
    font-size: 1.5rem;
  }

  .dashboard-header::after {
    width: 4rem;
  }

  .food-item-image {
    min-width: 5rem;
  }

  .food-item-name {
    font-size: 0.9rem;
  }

  .food-item-description {
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
  }

  .info-label,
  .info-value {
    font-size: 0.8125rem;
  }

  .order-info-row {
    margin-bottom: 0.5rem;
  }

  .popup-content {
    width: 95%;
  }

  .popup-header {
    padding: 1rem;
  }

  .popup-title {
    font-size: 1.25rem;
  }

  .role-button {
    padding: 0.75rem;
  }

  .role-icon {
    width: 2.5rem;
    height: 2.5rem;
    margin-right: 0.75rem;
  }

  .role-icon svg {
    width: 1.25rem;
    height: 1.25rem;
  }

  .role-name {
    font-size: 0.9rem;
  }
}

@media (max-width: 600px) {
  .food-grid {
    gap: 0.5rem;
  }

  .food-card {
    width: calc(50% - 0.25rem);
  }

  .food-image-container {
    height: 120px;
  }

  .food-content {
    padding: 0.5rem;
  }

  .food-name {
    font-size: 0.9rem;
  }

  .food-description-container {
    margin-bottom: 0.5rem;
  }

  .food-description {
    font-size: 1rem;
    line-height: 1.3;
  }

  .food-description.collapsed {
    -webkit-line-clamp: 1;
    line-clamp: 1;
    max-height: 1.3rem;
  }

  .quantity-btn svg {
    width: 18px;
    height: 18px;
  }

  .quantity-display {
    font-size: 0.9rem;
  }

  .add-to-cart-btn {
    padding: 0.5rem;
    font-size: 1rem;
  }
}

@media (min-width: 640px) {
  .statistics-cards-container {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .rider-info {
    padding: 0.5rem;
  }

  .rider-assigned-badge,
  .no-rider-badge {
    font-size: 0.625rem;
    padding: 0.125rem 0.375rem;
  }

  .form-actions {
    flex-direction: column;
  }

  .save-button,
  .cancel-button {
    width: 100%;
    justify-content: center;
  }

  .support-header h1 {
    font-size: 1.75rem;
  }

  .guides-list {
    grid-template-columns: 1fr;
  }

  .support-contact-options {
    flex-direction: column;
    align-items: stretch;
  }

  .support-contact-card {
    width: 100%;
  }

  .contact-button {
    width: 100%;
    text-align: center;
  }

  .complaints-header h1 {
    font-size: 1.75rem;
  }

  .suggestions-header h1 {
    font-size: 1.75rem;
  }

  .rating-container {
    justify-content: space-between;
    width: 100%;
  }

  .contact-header h1 {
    font-size: 1.75rem;
  }

  .contact-info {
    grid-template-columns: 1fr;
  }

  .contact-form-container h2 {
    font-size: 1.25rem;
  }

  .vendors-dashboard-title-section h1 {
    font-size: 1.5rem;
  }

  .vendors-dashboard-title-section p {
    font-size: 0.9rem;
  }

  .vendors-nav-tab {
    padding: 0.75rem 1rem;
    font-size: 0.8rem;
  }

  .vendors-dashboard-content {
    padding: 0.2rem;
  }

  .vendors-orders-grid {
    grid-template-columns: 1fr;
  }

  .vendors-detail-label,
  .vendors-detail-value {
    font-size: 0.9rem;
  }

  .payouts-table {
    display: block;
    overflow-x: auto;
  }

  .status-change-confirmation {
    padding: 0.75rem;
  }

  .confirmation-buttons {
    gap: 0.5rem;
  }

  .confirm-btn,
  .cancel-btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.8125rem;
  }

  .profile-fields-grid {
    grid-template-columns: 1fr;
  }

  .profile-title {
    font-size: 1.5rem;
  }

  .avatar-container {
    width: 6rem;
    height: 6rem;
  }

  .profile-section-title {
    font-size: 1rem;
  }

  .profile-submit-btn {
    width: 100%;
  }

  .orders-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-header h2 {
    font-size: 1.75rem;
  }

  .dashboard-subtitle {
    font-size: 1rem;
  }

  .order-number {
    font-size: 1.1rem;
  }

  .section-title {
    font-size: 1rem;
  }

  .dashboard-main {
    margin-left: 0;
    width: 100%;
  }

  .dashboard-main.expanded {
    margin-left: 0;
    width: 100%;
  }

  .dashboard-sidebar.collapsed {
    transform: translateX(-100%);
  }

  .cart-button {
    width: 2.5rem;
    height: 2.5rem;
    order: 3; /* Keep cart button last */
    margin-left: 1rem;
  }

  .cart-badge {
    width: 1rem;
    height: 1rem;
    font-size: 0.625rem;
  }

  .vendor-info-overlay h1 {
    font-size: 2.25rem;
  }

  .search-icon {
    margin-right: 1rem;
  }

  .search-container {
    bottom: -2rem;
  }

  .document-modal-content {
    max-width: 95%;
  }

  .document-modal-header {
    padding: 0.75rem 1rem;
  }

  .document-modal-header h3 {
    font-size: 1rem;
  }

  .document-modal-body {
    padding: 1rem;
  }

  .pagination-controls {
    flex-direction: column;
    align-items: center;
  }

  .per-page-control {
    justify-content: center;
    margin-top: 1rem;
  }

  .push-notification-card {
    padding: 2rem;
    border-radius: 1.25rem;
  }

  .push-notification-title h3 {
    font-size: 1.5rem;
  }

  .push-notification-subtitle {
    font-size: 1rem;
  }

  .push-notification-button {
    padding: 1rem;
    font-size: 1.1rem;
    border-radius: 0.75rem;
  }

  .push-notification-details {
    padding: 1.5rem;
  }

  .push-notification-list-item strong {
    font-size: 1rem;
  }

  .push-notification-list-item p {
    font-size: 0.9rem;
  }

  .dashboard-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 998;
    transition: opacity 0.3s ease;
  }

  .dashboard-overlay.active {
    display: block;
  }

  .dashboard-sidebar-toggle-mobile {
    display: flex;
    position: fixed;
    top: 1rem;
    left: 1rem;
    z-index: 1001;
    background: #f8b400;
    border: none;
    border-radius: 20%;
    width: 2.5rem;
    height: 2.5rem;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0.125rem 0.25rem rgba(248, 180, 0, 0.3);
    cursor: pointer;
    color: #fff;
    transition: all 0.3s ease;
  }

  .dashboard-sidebar-toggle-mobile:hover {
    background: #e5a700;
    color: #fff;
    box-shadow: 0 0.25rem 0.5rem rgba(248, 180, 0, 0.4);
    transform: translateY(-2px);
  }

  .dashboard-sidebar.open,
  .dashboard-sidebar:not(.collapsed) {
    transform: translateX(0);
  }

  .home-banner-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }

  .home-banner-subtitle {
    font-size: 1.25rem;
    max-width: 60%;
  }

  .lets-eat-btn:hover,
  .see-menu-btn:hover {
    transform: translateY(-0.25rem);
  }

  .desktop-menu-container {
    display: block;
    margin-left: auto;
    margin-right: 0;
    order: 2;
  }

  .desktop-icons-menu {
    display: none;
  }

  .desktop-icon-container {
    display: flex;
    align-items: center;
    background-color: transparent;
    border: none;
    cursor: pointer;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    margin: 0 0.25rem;
  }

  .desktop-icon-container p {
    color: #444;
    font-size: 0.85rem;
    margin: 0 0 0 0.5rem;
    font-weight: 500;
    letter-spacing: 0.2px;
  }

  .desktop-icon-container:hover {
    background-color: rgba(248, 180, 0, 0.08);
  }

  .desktop-icon-container:hover p {
    color: #f8b400;
  }

  .desktop-icon-container.active {
    background-color: rgba(248, 180, 0, 0.1);
  }

  .desktop-icon-container.active p {
    color: #f8b400;
    font-weight: 600;
  }

  .desktop-dropdown-container {
    position: absolute;
    top: 100%;
    right: 1rem;
    z-index: 1000;
  }

  .desktop-menu-dropdown {
    width: 220px;
    background-color: #fff;
    border-radius: 0.75rem;
    box-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.15);
    padding: 0.75rem 0;
    border: 1px solid rgba(248, 180, 0, 0.1);
    margin-top: 0.75rem;
    display: flex;
    flex-direction: column;
    animation: dropdown-appear 0.2s ease-out;
  }

  @keyframes dropdown-appear {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .desktop-menu-dropdown li {
    width: 100%;
    margin: 0;
    padding: 0;
  }

  .desktop-menu-dropdown li button {
    width: 100%;
    text-align: left;
    padding: 0.75rem 1.25rem;
    background-color: transparent;
    border: none;
    font-size: 0.95rem;
    color: #444;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    border-radius: 0;
  }

  .desktop-menu-dropdown li button:hover {
    background-color: rgba(248, 180, 0, 0.08);
    color: #f8b400;
    transform: translateX(0.25rem);
  }

  .desktop-menu-dropdown li:last-child button {
    color: #f44;
    margin-top: 0.5rem;
  }

  .desktop-menu-dropdown li:last-child button:hover {
    background-color: rgba(255, 68, 68, 0.1);
    color: #f44;
    transform: translateX(0.25rem);
  }
}

@media (min-width: 769px) {
  .dashboard-sidebar.collapsed {
    width: 70px;
    transform: translateX(0);
  }

  .dashboard-sidebar.collapsed .dashboard-logo span,
  .dashboard-sidebar.collapsed .dashboard-nav-item span {
    display: none;
  }

  .dashboard-sidebar.collapsed .dashboard-nav-item {
    justify-content: center;
    padding: 0.85rem 0;
    width: 100%;
    margin: 0.4rem 0;
  }

  .dashboard-sidebar.collapsed .dashboard-nav-icon {
    margin-right: 0;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .dashboard-sidebar.collapsed .dashboard-nav-badge {
    right: 0.5rem;
    top: 0.25rem;
  }

  .dashboard-main.expanded {
    margin-left: 70px;
    width: calc(100% - 70px);
  }
}

@media (min-width: 992px) {
  .auth-page {
    padding: 2rem 0.5rem;
    background-size: 30px 30px;
    background-color: #f5f7fa;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="%23f5f7fa"/><path d="M0 0L100 100M100 0L0 100" stroke="%23eef0f5" stroke-width="2"/></svg>');
  }

  .auth-form {
    max-width: 450px;
    max-height: none;
    overflow-y: visible;
    box-shadow: 0 1.5rem 4rem rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(248, 180, 0, 0.05);
    transform: translateY(0);
    border-radius: 1.25rem;
    border: 1px solid rgba(248, 180, 0, 0.1);
  }

  .auth-form:hover {
    transform: translateY(-5px);
    box-shadow: 0 2rem 5rem rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(248, 180, 0, 0.1);
  }

  .login-button {
    padding: 1rem !important;
    font-size: 1.2rem !important;
    border-radius: 0.625rem;
    margin-top: 2rem;
    letter-spacing: 0.5px;
  }

  .auth-form-header {
    padding: 1.75rem 2rem;
    border-radius: 1.25rem 1.25rem 0 0;
    text-align: left;
  }

  .auth-form h2 {
    font-size: 1.75rem;
    margin-bottom: 0.5rem;
  }

  .icons-menu {
    width: 70%;
    left: 10rem;
  }

  .dashboard-sidebar.collapsed {
    width: 100px;
  }

  .dashboard-sidebar.collapsed .dashboard-nav-item {
    width: 95%;
  }

  .dashboard-main.expanded {
    margin-left: 110px;
    width: calc(95% - 70px);
  }

  .dashboard-sidebar {
    left: 1.5rem;
  }

  .role-badge {
    display: inline-block;
    margin-top: 0;
    font-size: 0.9rem;
    padding: 0.25rem 1rem;
  }

  .auth-page .message {
    font-size: 0.95rem;
    text-align: left;
    margin-top: 0.75rem;
    max-width: 90%;
  }

  .search-bar input {
    margin-left: 2rem;
  }

  .auth-form-content {
    padding: 1rem;
  }

  .auth-form-content .form-group {
    margin-bottom: 1.5rem;
  }

  .auth-form-content label {
    font-size: 0.95rem;
    margin-bottom: 0.5rem;
    color: #444;
  }

  .form-input {
    padding: 0.875rem 1.25rem 0.875rem 2.75rem;
    font-size: 1.05rem;
    border-radius: 0.625rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
  }

  .vendor-card {
    width: 32%;
  }

  .vendor-container {
    gap: 1rem;
  }

  .auth-form-content .form-input {
    background-color: #fff;
    border: 1px solid #e0e0e0;
    border-bottom: 0.125rem solid #f8b400;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
  }

  .input-icon {
    font-size: 1.125rem;
    left: 1rem;
    color: #f8b400;
  }

  .auth-form-content .input-icon {
    color: #f8b400;
  }

  .form-steps {
    margin-bottom: 2.5rem;
  }

  .password-toggle {
    right: 1rem;
    font-size: 1.125rem;
  }

  .form-input:focus {
    box-shadow: 0 0 0 3px rgba(248, 180, 0, 0.15);
    transform: translateY(-1px);
  }

  .auth-form-footer {
    padding: 1rem;
    margin-top: 0;
  }

  .auth-page .account-link {
    font-size: 1rem;
  }

  .error-message {
    font-size: 0.9rem;
    padding: 0.625rem 1rem;
  }

  .search-container .search-bar .search-icon {
    left: 17rem;
  }
}

@media (min-width: 1024px) {
  .desktop-menu-container {
    display: block;
    margin-left: auto;
    margin-right: 0.5rem;
  }

  .desktop-icons-menu {
    display: flex;
    align-items: center;
  }

  .desktop-icons-menu ul {
    display: flex;
    list-style: none;
    gap: 0.5rem;
    margin: 0;
    padding: 0;
    align-items: center;
  }

  .desktop-icons-menu ul li {
    position: relative;
    display: flex;
    align-items: center;
  }

  .desktop-icons-menu .divider li:not(:last-child)::after {
    content: "";
    display: block;
    height: 1.5rem;
    width: 1px;
    background-color: rgba(0, 0, 0, 0.1);
    margin: 0 0.25rem;
  }

  .desktop-icons-menu ul li button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: none;
    color: #555;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
  }

  .desktop-icons-menu ul li button:hover {
    color: #f8b400;
    background-color: rgba(248, 180, 0, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(248, 180, 0, 0.1);
  }

  .desktop-icons-menu ul li button.active {
    color: #f8b400;
    font-weight: 600;
    background-color: rgba(248, 180, 0, 0.1);
  }

  .desktop-icons-menu ul li button svg {
    width: 1.25rem;
    height: 1.25rem;
    transition: all 0.2s ease;
    stroke-width: 2px;
  }

  .desktop-icons-menu ul li button:hover svg {
    transform: scale(1.1);
    color: #f8b400;
  }

  .desktop-icons-menu ul li button.active svg {
    color: #f8b400;
    stroke-width: 2.2px;
  }

  .desktop-dropdown-container {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.15);
    padding: 0.5rem;
    min-width: 12rem;
    z-index: 1000;
    margin-top: 0.75rem;
    animation: dropdown-appear 0.2s ease-out;
    border: 1px solid rgba(248, 180, 0, 0.1);
    transform-origin: top right;
  }

  @keyframes dropdown-appear {
    from {
      opacity: 0;
      transform: translateY(-10px) scale(0.95);
    }

    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  .desktop-dropdown-container ul {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .desktop-dropdown-container ul li button {
    width: 100%;
    text-align: left;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.9rem;
    color: #555;
    transition: all 0.2s ease;
    border: none;
    background: none;
    cursor: pointer;
    border-bottom: 0.2px solid #0000001a;
  }

  .desktop-dropdown-container ul li button:hover {
    background-color: rgba(248, 180, 0, 0.1);
    color: #f8b400;
    transform: translateX(3px);
    box-shadow: 0 2px 4px rgba(248, 180, 0, 0.05);
    font-weight: 500;
  }

  .desktop-dropdown-container ul li #logout-hover:hover {
    background-color: rgba(255, 0, 0, 0.2);
    color: #000;
  }

  .desktop-dropdown-container ul li button svg {
    color: #666;
    transition: all 0.2s ease;
  }

  .desktop-dropdown-container ul li button:hover svg {
    color: #f8b400;
  }

  .icons-menu {
    display: none;
  }

  .vendor-card {
    width: calc(24.6%);
  }

  .food-card {
    width: calc(32%) !important;
  }

  .food-grid {
    gap: 0.5rem !important;
  }

  .food-image-container {
    height: 300px !important;
  }

  .vendor-details {
    margin-bottom: 4rem !important;
  }

  .vendor-image {
    height: 150px; /* Taller image container for desktop */
  }

  .vendor-image img {
    width: 130px;
    height: 130px;
  }

  .see-menu-btn {
    bottom: 1.25rem;
    width: calc(100% - 2.5rem);
    padding: 0.75rem;
  }

  .statistics-cards-container {
    grid-template-columns: repeat(4, 1fr);
  }

  .home-dashboard-container,
  .food-dashboard-container {
    padding: 0 1rem 1rem !important;
    background-color: #f8f9fa;
    margin-top: 4.6rem !important;
  }

  .food-dashboard-container {
    max-width: 100% !important;
  }

  .dashboard-sidebar {
    width: 220px;
    left: 0;
  }

  .wave-banner {
    margin-top: 0;
  }

  .dashboard-nav-badge {
    height: 1rem !important;
    min-width: 1rem !important;
    font-size: 0.5rem !important;
    padding: 0.1rem !important;
  }

  .dashboard-sidebar-header,
  .dashboard-sidebar-toggle {
    padding: 0.5rem !important;
  }

  .dashboard-sidebar-toggle,
  .dashboard-logo-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 20%;
  }

  .orders-grid {
    grid-template-columns: repeat(auto-fill, minmax(290px, 1fr)) !important;
    gap: 1rem !important;
  }

  .customer-orders-container {
    margin: 6rem auto auto auto !important;
    width: 95%;
    max-width: 1600px !important;
  }

  .dashboard-main {
    margin-left: 230px !important;
    width: calc(100% - 240px);
  }

  .wave-banner,
  .vendors-dashboard-container {
    max-width: 1580px !important;
    margin: 0 auto;
  }

  .wave-banner {
    margin-top: 3rem !important;
  }

  .vendors-dashboard-container {
    padding: 0;
  }

  .statistics-section {
    margin-bottom: 0;
  }

  .dashboard-nav-item {
    padding: 0.5rem 0 !important;
    width: 90% !important;
  }

  .header {
    position: fixed !important;
    width: 100% !important;
    padding: 0.75rem 1rem !important;
  }

  .desktop-dropdown-container {
    top: 3.7rem;
    right: 10rem;
  }

  .desktop-dropdown-container ul {
    align-items: baseline;
  }

  .desktop-dropdown-container li {
    width: 100% !important;
  }

  .dashboard-main.expanded {
    margin-left: 105px !important;
    width: calc(100% - 110px) !important;
  }

  .contact-page,
  .profile-container,
  .complaints-page,
  .support-page,
  .suggestions-page {
    margin-top: 5rem !important;
    padding: 1.5rem 1rem 1rem !important;
  }

  .cart-container {
    margin-top: 7rem !important;
    max-width: 40rem !important;
  }

  .order-form,
  .payment-card {
    max-width: 35rem !important;
  }

  .contact-form-container,
  .contact-card {
    padding: 0.5rem !important;
  }

  .contact-card {
    width: 32%;
  }

  .contact-form-container {
    height: fit-content !important;
    width: 70%;
    min-width: 100%;
    margin: 0 auto;
  }

  .contact-container {
    flex-direction: column;
  }

  .contact-info {
    flex-direction: row !important;
    flex-wrap: wrap !important;
    gap: 0.5rem !important;
  }

  .hero-section {
    margin-bottom: 3rem;
  }

  .profile-container {
    padding: 1.5rem 0 1rem !important;
  }

  .home-banner,
  .food-hero-banner {
    margin-top: 0;
  }

  .home-banner-title {
    font-size: 3rem;
  }

  .categories-section,
  .vendors-section {
    margin-left: 0 !important;
    margin-right: 0 !important;
    max-width: 100% !important;
  }

  .home-banner-subtitle {
    font-size: 1.5rem;
    max-width: 50%;
  }

  .search-container {
    bottom: -2rem;
    left: 10rem;
    width: 40rem;
  }

  .search-bar {
    max-width: 700px;
  }

  .search-icon {
    left: 13rem !important;
  }

  .search-container .search-bar .search-icon {
    left: 5rem !important;
  }

  .categories {
    gap: 1.5rem;
    padding: 1rem 0.5rem;
  }

  .category-card {
    padding: 1.25rem;
    min-width: 130px;
  }

  .category-icon {
    width: 48px;
    height: 48px;
  }

  .vendors-section {
    margin: 2rem 0;
    padding-top: 2rem;
  }

  .vendor-container {
    gap: 0.32rem;
    padding: 1rem 0;
  }

  .categories-section,
  .vendors-section,
  .category-slider {
    padding: 0 !important;
  }

  .vendor-content h2 {
    font-size: 1.25rem;
    margin: 0 0 0.5rem 0;
  }

  .food-hero-banner {
    height: 350px;
  }

  .greeting-content {
    padding: 0.5rem 1rem;
  }

  .greeting-time {
    font-size: 0.8rem;
    margin-bottom: 0.2rem;
  }

  .user-name {
    font-size: 1rem;
  }

  .suggestions-container {
    flex-direction: column;
  }

  .suggestions-form-container,
  .submitted-suggestions {
    min-width: 100%;
  }

  .complaints-container {
    flex-direction: column;
  }

  .complaints-form-container,
  .submitted-complaints {
    min-width: 100%;
  }

  .support-content {
    flex-direction: column;
  }

  .support-sidebar {
    max-width: 100%;
  }

  .quick-links {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(15rem, 1fr));
    gap: 1rem;
  }
}


/* Vendors Dashboard — Action Buttons */
.vendors-action-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.35rem;
  padding: 0.45rem 0.7rem;
  border-radius: 0.5rem;
  border: 1px solid transparent;
  background-color: #f5f5f5;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  line-height: 1;
}
.vendors-action-btn + .vendors-action-btn { margin-left: 0.5rem; }
.vendors-action-btn:hover { background-color: #eee; transform: translateY(-1px); }
.vendors-edit-btn { background-color: #0ebce9; color: #fff; border-color: #0ebce9; }
.vendors-edit-btn:hover { background-color: #0da8d0; }
.vendors-delete-btn { background-color: #ff000c; color: #fff; border-color: #ff000c; }
.vendors-delete-btn:hover { background-color: #e0000a; }

/* Vendors Dashboard — Modal */
.vendors-modal-overlay {
  position: fixed;
  inset: 0;
  z-index: 2000;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  backdrop-filter: blur(6px) saturate(110%);
}

/* Modal container */
.vendors-modal {
  width: min(560px, 95vw);
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.92);
  backdrop-filter: blur(8px) saturate(120%);
  border-radius: 1rem;
  box-shadow: 0 24px 48px rgba(0, 0, 0, 0.25);
  border: 1px solid #f0f0f0;
  animation: vd-modal-in 160ms ease-out;
}

@keyframes vd-modal-in {
  from { opacity: 0; transform: translateY(-8px); }
  to { opacity: 1; transform: translateY(0); }
}

.vendors-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.9rem 1rem;
  background-color: #f8b400;
  color: #040505;
  border-radius: 0.5rem;
  border-bottom: 1px solid #f1f1f1;
}

.vendors-modal-close {
  background: #000;
  color: #fff;
  border: 0;
  width: 2rem;
  height: 2rem;
  border-radius: 0.5rem;
  cursor: pointer;
}

.vendors-modal-close:hover { 
  background: #e0000a; 
}

.vendors-modal-footer { 
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  padding: 0.75rem 1rem 1rem;
  border-top: 1px solid #f1f1f1;
}

.vendors-modal .form-group { 
  display: flex;
  flex-direction: column;
  gap: 0.4rem;
  margin-bottom: 0.85rem;
}

.vendors-modal .form-group label { 
  font-weight: 700;
  color: #040505;
  font-size: 0.95rem;
}

.vendors-modal input[type="text"],
.vendors-modal input[type="number"],
.vendors-modal textarea {
  width: 100%;
  padding: 0.6rem 0.75rem;
  border-radius: 0.5rem;
  border: 1px solid #e0e0e0;
  outline: none;
}

.vendors-modal input[type="text"]:focus,
.vendors-modal input[type="number"]:focus,
.vendors-modal textarea:focus { 
  border-color: #f8b400;
  box-shadow: 0 0 0 3px rgba(248, 180, 0, 0.15);
}

.vendors-modal textarea { 
  min-height: 100px;
  resize: vertical;
}

.vendors-modal .radio-row { 
  display: flex;
  align-items: center;
  gap: 1rem;
}

.vendors-modal .price-row { 
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

/* Buttons in modals: red for dangerous, #f8b400 for others */
.btn { 
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.35rem;
  padding: 0.6rem 0.9rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  font-weight: 700;
}

.btn-primary { 
  background-color: #f8b400;
  color: #fff;
}

.btn-primary:hover { 
  background-color: #e5a700;
}

.btn-danger { 
  background-color: #ff000c;
  color: #fff;
}

.btn-danger:hover { 
  background-color: #e0000a;
}

.btn-secondary { 
  background-color: #f0f0f0;
  color: #333;
}

.btn-secondary:hover { 
  background-color: #e6e6e6;
}

.btn-sm {
  padding: 0.35rem 0.6rem;
  font-size: 0.85rem;
  background-color: #f8b400;
}
