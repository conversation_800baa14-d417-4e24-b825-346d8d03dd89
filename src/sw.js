import { precacheAndRoute, cleanupOutdatedCaches } from 'workbox-precaching';
import { registerRoute } from 'workbox-routing';
import { CacheFirst, NetworkFirst } from 'workbox-strategies';
import { ExpirationPlugin } from 'workbox-expiration';
import { CacheableResponsePlugin } from 'workbox-cacheable-response';

// Precache all the assets generated by your build process
precacheAndRoute(self.__WB_MANIFEST);

// Clean up outdated caches
cleanupOutdatedCaches();

// Cache Google Fonts
registerRoute(
  /^https:\/\/fonts\.googleapis\.com\/.*/i,
  new CacheFirst({
    cacheName: 'google-fonts-cache',
    plugins: [
      new ExpirationPlugin({
        maxEntries: 10,
        maxAgeSeconds: 60 * 60 * 24 * 365, // 1 year
      }),
      new CacheableResponsePlugin({
        statuses: [0, 200],
      }),
    ],
  })
);

registerRoute(
  /^https:\/\/fonts\.gstatic\.com\/.*/i,
  new CacheFirst({
    cacheName: 'gstatic-fonts-cache',
    plugins: [
      new ExpirationPlugin({
        maxEntries: 10,
        maxAgeSeconds: 60 * 60 * 24 * 365, // 1 year
      }),
      new CacheableResponsePlugin({
        statuses: [0, 200],
      }),
    ],
  })
);

// Cache images
registerRoute(
  /\.(?:png|jpg|jpeg|svg|gif)$/i,
  new CacheFirst({
    cacheName: 'images-cache',
    plugins: [
      new ExpirationPlugin({
        maxEntries: 50,
        maxAgeSeconds: 60 * 60 * 24 * 30, // 30 days
      }),
    ],
  })
);

// Cache API requests
registerRoute(
  new RegExp('^https://.*\\.easefood\\.org/api'),
  new NetworkFirst({
    cacheName: 'api-cache',
    networkTimeoutSeconds: 10,
    plugins: [
      new ExpirationPlugin({
        maxEntries: 100,
        maxAgeSeconds: 60 * 60 * 24, // 24 hours
      }),
      new CacheableResponsePlugin({
        statuses: [0, 200],
      }),
    ],
  })
);

// PUSH NOTIFICATION HANDLING
self.addEventListener('push', function(event) {
  let notificationData = {
    title: 'EaseFood',
    body: 'You have a new notification',
    // Use PNG assets for better Android/iOS compatibility
    icon: self.location.origin + '/apple-touch-icon.png',
    badge: self.location.origin + '/apple-touch-icon.png',
    data: {},
    badgeCount: 1 // Default badge count
  };

  // Parse the push event data
  if (event.data) {
    try {
      const pushData = event.data.json();
          console.log('push data:', pushData);

      notificationData = {
        title: pushData.title || 'EaseFood',
        body: pushData.body || 'You have a new notification',
        icon: pushData.icon || self.location.origin + '/apple-touch-icon.png',
        badge: pushData.badge || self.location.origin + '/apple-touch-icon.png',
        tag: pushData.data?.order_id ? `order-${pushData.data.order_id}` : 'general',
        data: pushData.data || {},
        requireInteraction: true,
        badgeCount: pushData.badgeCount || 1
      };
    } catch (error) {
      // Silent error handling - could send to error tracking service in production
      // Example: sendErrorToTrackingService('push-notification-parse-error', error);
    }
  }

  // Update app badge if supported (for PWA home screen icon)
  const updateBadge = async () => {
    try {
      // Check if device is Android
      const isAndroid = /Android/i.test(self.navigator.userAgent);

      if (isAndroid) {
        // On Android, badges are handled automatically by the system when notifications are shown
        // The badge will appear automatically when this push notification is displayed
        console.log(`🤖 Android: Badge will be handled automatically by system. Count: ${notificationData.badgeCount}`);
        return;
      }

      // For iOS, Windows, macOS - use the Badging API
      if (self.registration.setAppBadge && notificationData.badgeCount !== undefined) {
        if (notificationData.badgeCount > 0) {
          await self.registration.setAppBadge(notificationData.badgeCount);
          console.log(`📱 PWA Badge updated from push notification: ${notificationData.badgeCount}`);
        } else {
          await self.registration.clearAppBadge();
          console.log('📱 PWA Badge cleared from push notification');
        }
      }
    } catch (error) {
      // Silent fail for badge updates
      console.warn('❌ Failed to update PWA app badge from push notification:', error);
    }
  };

  // Show the notification
  const promiseChain = (async () => {
    // iOS Safari requires a visible icon/soundless notification; 'badge' is ignored
    const options = {
      body: notificationData.body,
      icon: notificationData.icon,
      // image: notificationData.image, // Optional hero image
      tag: notificationData.tag,
      data: notificationData.data,
      requireInteraction: notificationData.requireInteraction,
    };

    try {
      await self.registration.showNotification(notificationData.title, options);
    } catch (err) {
      // Some Android OEMs may require PNGs; already using apple-touch-icon.png
      console.warn('showNotification failed', err);
    }
    await updateBadge();
  })();

  event.waitUntil(promiseChain);
});

// Handle notification clicks
self.addEventListener('notificationclick', function(event) {
  event.notification.close();

  // Handle action buttons
  if (event.action === 'view') {
    const orderUrl = event.notification.data?.url || '/';
    event.waitUntil(
      clients.openWindow(orderUrl)
    );
  } else if (event.action === 'dismiss') {
    // Just close the notification (already done above)
    return;
  } else {
    // Default click action - open the app
    const urlToOpen = event.notification.data?.url || '/';

    event.waitUntil(
      clients.matchAll({
        type: 'window',
        includeUncontrolled: true
      }).then(function(clientList) {
        // Check if app is already open
        for (let i = 0; i < clientList.length; i++) {
          const client = clientList[i];
          if (client.url.includes(self.location.origin) && 'focus' in client) {
            // Send message to client to play notification sound
            client.postMessage({
              type: 'PLAY_NOTIFICATION_SOUND',
              role: event.notification.data?.userRole || 'general'
            });

            // Navigate to the specific URL if needed
            if (event.notification.data.url) {
              client.navigate(event.notification.data.url);
            }
            return client.focus();
          }
        }

        // If app is not open, open it
        if (clients.openWindow) {
          return clients.openWindow(urlToOpen);
        }
      })
    );
  }
});

// Handle notification close events
self.addEventListener('notificationclose', function(event) {
  // Optional: Send analytics data about notification dismissal
  // Example: sendAnalyticsEvent('notification-dismissed', { tag: event.notification.tag });
});

// Listen for messages from the client
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});


// Ensure new service worker takes control immediately after activation
self.addEventListener('activate', (event) => {
  event.waitUntil(self.clients.claim());
});

// Skip waiting and claim clients immediately
self.addEventListener('install', function(event) {
  // Remove automatic skipWaiting() - let the user decide
  // self.skipWaiting();
});

// Don't automatically claim clients on activate
self.addEventListener('activate', function(event) {
  event.waitUntil(
    self.clients.claim().then(() => {
      // Reload all clients to apply the update
      self.clients.matchAll().then(clients => {
        clients.forEach(client => {
          client.navigate(client.url);
        });
      });
    })
  );
});