import { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import axios from 'axios';
import { toast } from 'react-toastify';
import ApiUrl from '../helper-functions/ApiUrl';
import { initializePushNotifications, isPushNotificationSupported } from './pushNotifications';
import { createOrder, updateOrderStatus } from '../../redux/slice/ordersSlice';

const PushNotificationTester = () => {
  const dispatch = useDispatch();
  const { isAuthenticated, user } = useSelector((state) => state.auth);
  const [logs, setLogs] = useState([]);
  const [creating, setCreating] = useState(false);
  const [initializing, setInitializing] = useState(false);

  const appendLog = (msg) => setLogs((prev) => [...prev, `${new Date().toLocaleTimeString()}: ${msg}`]);

  const getAuthToken = () => {
    try {
      const authData = localStorage.getItem('authData');
      if (!authData) return null;
      const parsed = JSON.parse(authData);
      return parsed?.token || null;
    } catch { return null; }
  };

  const handleInitPush = async () => {
    if (!isPushNotificationSupported()) {
      toast.error('Push not supported in this browser');
      return;
    }
    const token = getAuthToken();
    if (!token) {
      toast.error('Auth token not found. Please log in.');
      return;
    }
    try {
      setInitializing(true);
      appendLog('Initializing push notifications...');
      await initializePushNotifications(token);
      const reg = await navigator.serviceWorker.ready;
      const sub = await reg.pushManager.getSubscription();
      if (Notification.permission === 'granted' && sub) {
        appendLog(`Subscribed: ${sub.endpoint.slice(0, 48)}...`);
        toast.success('Push initialized and subscribed');
      } else {
        appendLog('Subscription not found after init');
        toast.warn('Push initialized but subscription missing');
      }
    } catch (e) {
      appendLog(`Init error: ${e.message}`);
      toast.error(e.message);
    } finally {
      setInitializing(false);
    }
  };

  const handleCreateConfirmedOrder = async () => {
    // NOTE: You must be logged in as the target customer for this to work (customer id 9)
    const token = getAuthToken();
    if (!token) {
      toast.error('Auth token not found. Please log in as Customer (id: 9).');
      return;
    }

    const targetVendorId = 5; // as requested
    const targetCustomerId = 9; // as requested

    try {
      setCreating(true);
      appendLog('Fetching a food from vendor 5...');
      const foodsResp = await axios.get(`${ApiUrl}/vendors/${targetVendorId}/foods`);
      const foods = Array.isArray(foodsResp.data) ? foodsResp.data : (foodsResp.data?.foods || []);
      if (!foods || foods.length === 0) {
        appendLog('No foods found for vendor 5');
        toast.error('No foods for vendor 5');
        return;
      }
      const food = foods[0];
      appendLog(`Using food: ${food.name} (id: ${food.id})`);

      // Build order input
      const orderInput = {
        customerId: targetCustomerId,
        riderId: 2,
        foodIds: [food.id],
        quantities: [1],
        priceIndices: [0], // default first price/size
        deliveryAddress: 'Dunkwa, Push Test',
        deliveryPrice: 0,
        donation: 0,
      };

      appendLog('Creating order...');
      const result = await dispatch(createOrder(orderInput)).unwrap();

      let orderId = null;
      if (result.orders && Array.isArray(result.orders) && result.orders[0]?.id) {
        orderId = result.orders[0].id;
      } else {
        orderId = result.order_id || result.orderId || result.id;
      }
      if (!orderId) {
        appendLog(`Create response missing order id: ${JSON.stringify(result)}`);
        toast.error('Order created but id not found');
        return;
      }
      appendLog(`Order created: ${orderId}`);

      appendLog('Marking order as confirmed (vendor 5)...');
      await dispatch(updateOrderStatus({ orderId, status: 'confirmed', vendorId: targetVendorId })).unwrap();
      appendLog('Status updated to confirmed. Push should trigger to vendor and customer.');
      toast.success('Order confirmed, check for push');
    } catch (e) {
      const msg = e?.message || e?.toString();
      appendLog(`Error: ${msg}`);
      toast.error(msg);
    } finally {
      setCreating(false);
    }
  };

  return (
    <div style={{ maxWidth: 800, margin: '1rem auto', background: '#fff', border: '1px solid #eee', borderRadius: 12, padding: '1rem' }}>
      <h2>Push Notification Tester</h2>
      <p>Login states: {isAuthenticated ? 'Authenticated' : 'Not authenticated'} | Current user id: {user?.id || 'n/a'} ({user?.role || 'n/a'})</p>

      <div style={{ display: 'flex', gap: '.5rem', marginBottom: '.75rem' }}>
        <button type="button" className="btn btn-primary" disabled={initializing} onClick={handleInitPush}>
          {initializing ? 'Initializing...' : 'Initialize Push'}
        </button>
        <button type="button" className="btn btn-primary" disabled={creating} onClick={handleCreateConfirmedOrder}>
          {creating ? 'Creating...' : 'Create Confirmed Order (Vendor 5, Customer 9)'}
        </button>
      </div>

      <div style={{ background: '#fafafa', border: '1px solid #eee', borderRadius: 8, padding: '.75rem' }}>
        <div style={{ fontWeight: 700, marginBottom: '.5rem' }}>Logs</div>
        <div style={{ maxHeight: 240, overflow: 'auto', fontFamily: 'monospace', fontSize: 12 }}>
          {logs.map((l, i) => (
            // eslint-disable-next-line react/no-array-index-key
            <div key={i}>{l}</div>
          ))}
        </div>
      </div>

      <div style={{ marginTop: '1rem', fontSize: 12, color: '#555' }}>
        <p>
          Instructions:
          <br />1) Open one browser/profile as Customer (id: 9) and visit /push-test to Initialize Push.
          <br />2) Open another browser/profile as Vendor (id: 5) and visit /push-test to Initialize Push.
          <br />3) In the customer window, click "Create Confirmed Order". This will create an order for customer 9 with a food from vendor 5, then immediately mark it confirmed. Both sides should receive a push.
        </p>
      </div>
    </div>
  );
};

export default PushNotificationTester;

