import { useEffect, useState, useMemo, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { Carousel } from 'react-responsive-carousel';
import 'react-responsive-carousel/lib/styles/carousel.min.css';
import {
  Search,
  Star,
  Clock,
  MapPin,
  Heart,
  ChevronRight,
  Utensils,
} from 'lucide-react';
import {
  fetchVendors, selectVendors, selectVendorsStatus, selectVendorsError,
} from '../../redux/slice/vendersSlice';
import Loader from '../helper-functions/Loader';
import AutoRefresh from '../helper-functions/AutoRefresh';

import food from '../../assets/food.png';
import foodTwo from '../../assets/foood.png';
import noodles from '../../assets/noodles.png';
import allI from '../../assets/all.png';
import dessert from '../../assets/dessert.png';
import beer from '../../assets/beer.png';
import snack from '../../assets/snack.png';
import rice from '../../assets/rice.png';
import dinner from '../../assets/dinner.png';
import noodle from '../../assets/noodle.png';
import others from '../../assets/others.png';

const HomeDashboard = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const vendors = useSelector(selectVendors);
  const status = useSelector(selectVendorsStatus);
  const error = useSelector(selectVendorsError);
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedDescriptions, setExpandedDescriptions] = useState({});

  useEffect(() => {
    dispatch(fetchVendors());
  }, [dispatch]);

  const handleLetsEatClick = (vendorId) => {
    navigate(`/food-dashboard/${vendorId}`);
  };

  // Auto-refresh function
  const handleRefresh = useCallback(async () => {
    try {
      await dispatch(fetchVendors()).unwrap();
      // Silent refresh - no toast messages
    } catch (error) {
      console.error('Refresh error:', error);
    }
  }, [dispatch]);

  const categories = [
    { label: 'All', icon: allI },
    { label: 'Desserts', icon: dessert },
    { label: 'Drinks (Soft & Local)', icon: beer },
    { label: 'Snacks & Pastries', icon: snack },
    { label: 'Rice Dishes', icon: rice },
    { label: 'Noodles & Pasta', icon: noodle },
    { label: 'International Cuisine', icon: dinner },
    { label: 'Others', icon: others },
  ];

  // Memoize filteredVendors based on vendors, category, and search
  const filteredVendors = useMemo(() => {
    let filtered = vendors;

    // Filter by category
    if (selectedCategory !== 'All') {
      filtered = filtered.filter((vendor) => (vendor.category_names || [])
        .some((name) => name.toLowerCase() === selectedCategory.toLowerCase()));
    }

    // Filter by search query
    if (searchQuery.trim() !== '') {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter((vendor) => (
        vendor.name.toLowerCase().includes(query)
        || (vendor.vendor_description && vendor.vendor_description.toLowerCase().includes(query))
      ));
    }

    return filtered;
  }, [vendors, selectedCategory, searchQuery]);

  if (status === 'loading') {
    return <Loader />;
  }

  if (status === 'failed') {
    return (
      <div className="loading">
        Error: No vendors available.
        {' '}
        {error}
      </div>
    );
  }

  return (
    <main className="home-dashboard-container">
      {/* Hero Section with Carousel */}
      <section className="hero-section">
        <Carousel
          showThumbs={false}
          showStatus={false}
          showArrows
          infiniteLoop
          autoPlay
          interval={5000}
          className="hero-carousel"
        >
          <div className="home-banner">
            <img src={food} alt="Delicious food" />
          </div>
          <div className="home-banner">
            <img src={foodTwo} alt="Fresh meals" />
          </div>
        </Carousel>

        {/* Search Bar */}
        <div className="search-container">
          <div className="search-bar">
            <Search size={20} className="search-icon" />
            <input
              type="text"
              placeholder="Search for restaurants or food..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
      </section>

      {/* Category Section */}
      <section className="categories-section">
        <div className="section-header">
          <h2>Categories</h2>
        </div>
        <div className="category-slider">
          <div className="categories">
            {categories.map((category) => (
              <div
                key={category.label}
                className={`category-card ${selectedCategory === category.label ? 'selected' : ''}`}
                onClick={() => setSelectedCategory(category.label)}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    setSelectedCategory(category.label);
                  }
                }}
              >
                <img src={category.icon} alt={category.label} className="category-icon" />
                <p>{category.label}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Vendor Cards Section */}
      <section className="vendors-section">
        <div className="section-header">
          <h2>Popular Restaurants</h2>
          {searchQuery && (
            <div className="search-results-info">
              Showing results for
              &quot;
              {searchQuery}
              &quot;
            </div>
          )}
        </div>

        {/* Auto-refresh component */}
        <AutoRefresh 
          onRefresh={handleRefresh}
          interval={600000} // 10 minutes
        />

        <div className="vendor-container">
          {filteredVendors.length === 0 ? (
            <div className="no-results">
              <Utensils size={48} />
              <p>No restaurants found</p>
              {selectedCategory !== 'All' && (
                <p className="no-results-suggestion">Try a different category or search term</p>
              )}
            </div>
          ) : (
            filteredVendors.map((vendor) => (
              <div key={vendor.id} className="vendor-card">
                <div className="vendor-image">
                  <img src={vendor.vendor_url || noodles} alt={vendor.name} />
                </div>
                <div className="vendor-content">
                  <div className="vendor-header-row">
                    <h2>{vendor.name}</h2>
                    <div className="vendor-rating">
                      <Star size={14} className="star-icon" />
                      <span>4.5</span>
                    </div>
                  </div>

                  <div className="vendor-description-container">
                    <p className={`vendor-description ${expandedDescriptions[vendor.id] ? 'expanded' : 'collapsed'}`}>
                      {vendor.vendor_description}
                    </p>
                    {vendor.vendor_description && vendor.vendor_description.length > 50 && (
                      <button
                        type="button"
                        className="description-toggle"
                        onClick={(e) => {
                          e.stopPropagation();
                          setExpandedDescriptions((prev) => ({
                            ...prev,
                            [vendor.id]: !prev[vendor.id],
                          }));
                        }}
                      >
                        {expandedDescriptions[vendor.id] ? 'See Less' : 'See More'}
                        {expandedDescriptions[vendor.id]
                          ? <ChevronRight size={12} style={{ transform: 'rotate(-90deg)' }} />
                          : <ChevronRight size={12} style={{ transform: 'rotate(90deg)' }} />}
                      </button>
                    )}
                  </div>

                  <div className="vendor-details">
                    <div className="vendor-detail">
                      <MapPin size={14} className="detail-icon" />
                      <span>{vendor.address}</span>
                    </div>

                    <div className="vendor-detail">
                      <Clock size={14} className="detail-icon" />
                      <span>
                        {new Date(vendor.operation_time).toLocaleTimeString('en-US', {
                          hour: 'numeric',
                          minute: '2-digit',
                          hour12: true,
                          timeZone: 'Africa/Accra',
                        })}
                        {' - '}
                        {new Date(vendor.closing_time).toLocaleTimeString('en-US', {
                          hour: 'numeric',
                          minute: '2-digit',
                          hour12: true,
                          timeZone: 'Africa/Accra',
                        })}
                      </span>
                    </div>
                  </div>

                  <button
                    type="button"
                    className={`see-menu-btn ${!vendor.is_open ? 'disabled' : ''}`}
                    onClick={() => vendor.is_open && handleLetsEatClick(vendor.id)}
                    disabled={!vendor.is_open}
                  >
                    {vendor.is_open ? 'See Menu' : 'Closed'}
                    {vendor.is_open && <ChevronRight size={14} />}
                  </button>
                </div>

                <button type="button" className="vendor-favorite" aria-label="Add to favorites">
                  <Heart size={16} className="heart-icon" />
                </button>
              </div>
            ))
          )}
        </div>
      </section>
    </main>
  );
};

export default HomeDashboard;
