import React, {
  useEffect, useState, lazy, Suspense, useMemo,
} from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  FaShoppingCart, FaMoneyBillWave, FaPercentage,
  FaStore, FaUserCheck, FaMotorcycle, FaUsers, FaTags,
  FaBars, FaTimes, FaUtensils, FaExclamationTriangle,
  FaEye, FaCheck, FaChartLine, FaInfoCircle,
  FaBell, FaSpinner, FaPlus, FaTag, FaEdit, FaTrash,
  FaExpand, FaCommentAlt, FaLightbulb, FaClipboardCheck,
  FaTimes as FaTimesCircle, FaUserPlus, FaClock,
} from 'react-icons/fa';
import {
  fetchDashboardData,
  fetchAdminOrders,
  fetchAdminPayouts,
  fetchServiceFees,
  fetchVendors,
  fetchPendingVendors,
  fetchRiders,
  fetchPendingRiders,
  fetchCustomers,
  fetchCategories,
  fetchComplaints,
  fetchComplaintStatistics,
  fetchSuggestions,
  fetchSuggestionStatistics,
  fetchCustomerStatistics,
  fetchRiderStatistics,
  fetchVendorStatistics,
  updateServiceFees,
  updateComplaintStatus,
  updateSuggestionStatus,
  approveVendor,
  rejectVendor,
  approveRider,
  rejectRider,
  setActiveTab,
  setServiceFees,
  selectDashboardData,
  selectAdminOrders,
  selectAdminPayouts,
  selectServiceFees,
  selectVendors,
  selectPendingVendors,
  selectRiders,
  selectPendingRiders,
  selectCustomers,
  selectCategories,
  selectComplaints,
  selectSuggestions,
  selectComplaintStatistics,
  selectSuggestionStatistics,
  selectCustomerStatistics,
  selectRiderStatistics,
  selectVendorStatistics,
  selectActiveTab,
  selectAdminStatus,
  selectAdminError,
} from '../../redux/slice/adminSlice';
import Loader from '../helper-functions/Loader';
import './AdminDashboard.css';

// Lazy load the DocumentModal component
const DocumentModal = lazy(() => import('./DocumentModal'));

const AdminDashboard = () => {
  const { user, isAuthenticated } = useSelector((state) => state.auth);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  // Set sidebar collapsed by default on mobile
  const [sidebarCollapsed, setSidebarCollapsed] = useState(window.innerWidth <= 768);

  // Document modal state
  const [modalOpen, setModalOpen] = useState(false);
  const [modalImage, setModalImage] = useState('');
  const [modalTitle, setModalTitle] = useState('');

  // Function to open document modal
  const openDocumentModal = (imageUrl, title) => {
    setModalImage(imageUrl);
    setModalTitle(title);
    setModalOpen(true);
  };

  // Memoize user initials for avatar to avoid recalculating on every render
  const userInitial = useMemo(() => {
    if (!user || !user.name) return 'A';
    const names = user.name.split(' ');
    if (names.length === 1) return names[0].charAt(0).toUpperCase();
    return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase();
  }, [user]);

  // Select data from Redux store
  const dashboardData = useSelector(selectDashboardData);
  const orders = useSelector(selectAdminOrders);
  const payouts = useSelector(selectAdminPayouts);
  const serviceFees = useSelector(selectServiceFees);
  const vendors = useSelector(selectVendors);
  const pendingVendors = useSelector(selectPendingVendors);
  const riders = useSelector(selectRiders);
  const pendingRiders = useSelector(selectPendingRiders);
  const customers = useSelector(selectCustomers);
  const categories = useSelector(selectCategories);
  const complaints = useSelector(selectComplaints);
  const suggestions = useSelector(selectSuggestions);
  const complaintStatistics = useSelector(selectComplaintStatistics);
  const suggestionStatistics = useSelector(selectSuggestionStatistics);

  const customerStatistics = useSelector(selectCustomerStatistics);
  const riderStatistics = useSelector(selectRiderStatistics);
  const vendorStatistics = useSelector(selectVendorStatistics);
  const activeTab = useSelector(selectActiveTab);
  const status = useSelector(selectAdminStatus);
  const error = useSelector(selectAdminError);

  // Handle service fee input changes
  const handleServiceFeeChange = (e) => {
    const { name, value } = e.target;
    const updatedFees = {
      ...serviceFees,
      [name]: parseFloat(value),
    };
    // Update the local state, but only send to backend when user clicks update button
    dispatch(setServiceFees(updatedFees));
  };

  // Handle vendor approval
  const handleApproveVendor = (vendorId) => {
    dispatch(approveVendor(vendorId));
  };

  // Handle vendor rejection
  const handleRejectVendor = (vendorId) => {
    dispatch(rejectVendor(vendorId));
  };

  // Handle rider approval
  const handleApproveRider = (riderId) => {
    dispatch(approveRider(riderId));
  };

  // Handle rider rejection
  const handleRejectRider = (riderId) => {
    dispatch(rejectRider(riderId));
  };

  // Handle complaint status update
  const handleUpdateComplaintStatus = (id, status) => {
    dispatch(updateComplaintStatus({ id, status }));
  };

  // Handle suggestion status update
  const handleUpdateSuggestionStatus = (id, status) => {
    dispatch(updateSuggestionStatus({ id, status }));
  };

  // Check if user is admin
  useEffect(() => {
    if (!isAuthenticated || user?.role !== 'admin') {
      toast.error('Access denied. Admin privileges required.');
      navigate('/');
    }
  }, [isAuthenticated, user, navigate]);

  // Handle responsive sidebar behavior
  useEffect(() => {
    const handleResize = () => {
      // Only auto-collapse on mobile
      if (window.innerWidth <= 768) {
        setSidebarCollapsed(true);
      }
      // On desktop, we don't auto-expand anymore to allow user preference
      // This allows the sidebar to stay collapsed if the user wants it that way
    };

    // Set initial state
    handleResize();

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Initial data fetch for pending counts and badge displays
  useEffect(() => {
    // Fetch pending vendors and riders on component mount for badge display
    dispatch(fetchPendingVendors());
    dispatch(fetchPendingRiders());

    // Fetch complaints and suggestions for badge display
    dispatch(fetchComplaints());
    dispatch(fetchComplaintStatistics());

    dispatch(fetchSuggestions());
    dispatch(fetchSuggestionStatistics());

    // Fetch customer, rider, and vendor statistics
    dispatch(fetchCustomerStatistics());
    dispatch(fetchRiderStatistics());
    dispatch(fetchVendorStatistics());
  }, [dispatch]);

  // Load data based on active tab
  useEffect(() => {
    if (activeTab === 'dashboard') {
      dispatch(fetchDashboardData());
      dispatch(fetchCustomerStatistics());
    } else if (activeTab === 'orders') {
      dispatch(fetchAdminOrders());
    } else if (activeTab === 'payouts') {
      dispatch(fetchAdminPayouts());
    } else if (activeTab === 'service-fees') {
      dispatch(fetchServiceFees());
    } else if (activeTab === 'vendors') {
      dispatch(fetchVendors());
      dispatch(fetchVendorStatistics());
    } else if (activeTab === 'pending-vendors') {
      dispatch(fetchPendingVendors());
    } else if (activeTab === 'riders') {
      dispatch(fetchRiders());
      dispatch(fetchRiderStatistics());
    } else if (activeTab === 'pending-riders') {
      dispatch(fetchPendingRiders());
    } else if (activeTab === 'customers') {
      dispatch(fetchCustomers());
      dispatch(fetchCustomerStatistics());
    } else if (activeTab === 'categories') {
      dispatch(fetchCategories());
    } else if (activeTab === 'complaints') {
      dispatch(fetchComplaints());
      dispatch(fetchComplaintStatistics());
    } else if (activeTab === 'suggestions') {
      dispatch(fetchSuggestions());
      dispatch(fetchSuggestionStatistics());
    }
  }, [activeTab, dispatch]);

  // Memoize badge counts to avoid recalculating on every render
  const badgeCounts = useMemo(() => ({
    orders: orders?.length || 0,
    pendingVendors: pendingVendors?.length || 0,
    pendingRiders: pendingRiders?.length || 0,
    complaints: complaints?.length || 0,
    suggestions: suggestions?.length || 0,
  }), [orders, pendingVendors, pendingRiders, complaints, suggestions]);

  // Memoize dashboard alerts to avoid recalculating on every render
  const dashboardAlerts = useMemo(() => {
    if (!dashboardData || !dashboardData.counts) {
      return [];
    }

    const alerts = [];

    if (dashboardData.counts.pending_vendors > 0) {
      alerts.push({
        id: 'pending-vendors',
        icon: <FaUserCheck />,
        count: dashboardData.counts.pending_vendors,
        text: 'vendors pending approval',
        tab: 'pending-vendors',
      });
    }

    if (dashboardData.counts.pending_riders > 0) {
      alerts.push({
        id: 'pending-riders',
        icon: <FaClock />,
        count: dashboardData.counts.pending_riders,
        text: 'riders pending approval',
        tab: 'pending-riders',
      });
    }

    if (dashboardData.counts.pending_complaints > 0) {
      alerts.push({
        id: 'pending-complaints',
        icon: <FaCommentAlt />,
        count: dashboardData.counts.pending_complaints,
        text: 'complaints pending review',
        tab: 'complaints',
      });
    }

    if (dashboardData.counts.pending_suggestions > 0) {
      alerts.push({
        id: 'pending-suggestions',
        icon: <FaLightbulb />,
        count: dashboardData.counts.pending_suggestions,
        text: 'suggestions pending review',
        tab: 'suggestions',
      });
    }

    return alerts;
  }, [dashboardData]);

  if (status === 'loading') return <Loader />;

  // Function to toggle sidebar and handle overlay
  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  return (
    <div className="admin-dashboard">
      {/* Document Modal */}
      {modalOpen && (
        <Suspense fallback={<div className="loading-modal">Loading document...</div>}>
          <DocumentModal
            imageUrl={modalImage}
            title={modalTitle}
            onClose={() => setModalOpen(false)}
          />
        </Suspense>
      )}
      {/* Background Overlay */}
      <div
        className={`admin-overlay ${!sidebarCollapsed ? 'active' : ''}`}
        onClick={() => setSidebarCollapsed(true)}
        role="presentation"
      />

      {/* Mobile Toggle Button */}
      <button
        className="admin-sidebar-toggle"
        onClick={toggleSidebar}
        type="button"
        aria-label="Toggle navigation"
      >
        {sidebarCollapsed ? <FaBars /> : <FaTimes />}
      </button>

      {/* Sidebar Navigation */}
      <div className={`admin-sidebar ${sidebarCollapsed ? 'collapsed' : ''}`}>
        <div className="admin-sidebar-header">
          <div className="admin-logo">
            <FaUtensils className="admin-logo-icon" />
            <span>EaseFood Admin</span>
          </div>
          <button
            className="admin-sidebar-toggle desktop-toggle"
            onClick={toggleSidebar}
            type="button"
            aria-label="Toggle navigation"
          >
            {sidebarCollapsed ? <FaBars /> : <FaTimes />}
          </button>
        </div>

        <nav className="admin-nav">
          <button
            type="button"
            className={`admin-nav-item ${activeTab === 'dashboard' ? 'active' : ''}`}
            onClick={() => {
              dispatch(setActiveTab('dashboard'));
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <FaChartLine className="admin-nav-icon" />
            <span>Dashboard</span>
          </button>

          <button
            type="button"
            className={`admin-nav-item ${activeTab === 'orders' ? 'active' : ''}`}
            onClick={() => {
              dispatch(setActiveTab('orders'));
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <FaShoppingCart className="admin-nav-icon" />
            <span>Orders</span>
            {badgeCounts.orders > 0 && (
              <div className="admin-nav-badge">{badgeCounts.orders}</div>
            )}
          </button>

          <button
            type="button"
            className={`admin-nav-item ${activeTab === 'payouts' ? 'active' : ''}`}
            onClick={() => {
              dispatch(setActiveTab('payouts'));
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <FaMoneyBillWave className="admin-nav-icon" />
            <span>Payouts</span>
          </button>

          <button
            type="button"
            className={`admin-nav-item ${activeTab === 'service-fees' ? 'active' : ''}`}
            onClick={() => {
              dispatch(setActiveTab('service-fees'));
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <FaPercentage className="admin-nav-icon" />
            <span>Service Fees</span>
          </button>

          <button
            type="button"
            className={`admin-nav-item ${activeTab === 'vendors' ? 'active' : ''}`}
            onClick={() => {
              dispatch(setActiveTab('vendors'));
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <FaStore className="admin-nav-icon" />
            <span>Vendors</span>
          </button>

          <button
            type="button"
            className={`admin-nav-item ${activeTab === 'pending-vendors' ? 'active' : ''}`}
            onClick={() => {
              dispatch(setActiveTab('pending-vendors'));
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <FaUserCheck className="admin-nav-icon" />
            <span>Pending Vendors</span>
            {badgeCounts.pendingVendors > 0 && (
              <div className="admin-nav-badge">{badgeCounts.pendingVendors}</div>
            )}
          </button>

          <button
            type="button"
            className={`admin-nav-item ${activeTab === 'riders' ? 'active' : ''}`}
            onClick={() => {
              dispatch(setActiveTab('riders'));
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <FaMotorcycle className="admin-nav-icon" />
            <span>Riders</span>
          </button>

          <button
            type="button"
            className={`admin-nav-item ${activeTab === 'pending-riders' ? 'active' : ''}`}
            onClick={() => {
              dispatch(setActiveTab('pending-riders'));
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <FaUserCheck className="admin-nav-icon" />
            <span>Pending Riders</span>
            {badgeCounts.pendingRiders > 0 && (
              <div className="admin-nav-badge">{badgeCounts.pendingRiders}</div>
            )}
          </button>

          <button
            type="button"
            className={`admin-nav-item ${activeTab === 'customers' ? 'active' : ''}`}
            onClick={() => {
              dispatch(setActiveTab('customers'));
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <FaUsers className="admin-nav-icon" />
            <span>Customers</span>
          </button>

          <button
            type="button"
            className={`admin-nav-item ${activeTab === 'categories' ? 'active' : ''}`}
            onClick={() => {
              dispatch(setActiveTab('categories'));
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <FaTags className="admin-nav-icon" />
            <span>Categories</span>
          </button>

          <button
            type="button"
            className={`admin-nav-item ${activeTab === 'complaints' ? 'active' : ''}`}
            onClick={() => {
              dispatch(setActiveTab('complaints'));
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <FaCommentAlt className="admin-nav-icon" />
            <span>Complaints</span>
            {badgeCounts.complaints > 0 && (
              <div className="admin-nav-badge">{badgeCounts.complaints}</div>
            )}
          </button>

          <button
            type="button"
            className={`admin-nav-item ${activeTab === 'suggestions' ? 'active' : ''}`}
            onClick={() => {
              dispatch(setActiveTab('suggestions'));
              if (window.innerWidth <= 768) setSidebarCollapsed(true);
            }}
          >
            <FaLightbulb className="admin-nav-icon" />
            <span>Suggestions</span>
            {badgeCounts.suggestions > 0 && (
              <div className="admin-nav-badge">{badgeCounts.suggestions}</div>
            )}
          </button>
        </nav>
      </div>

      {/* Main Content Area */}
      <div className={`admin-main ${sidebarCollapsed ? 'expanded' : ''}`}>
        <div className="admin-header">
          <h1 className="admin-title">
            {activeTab === 'dashboard' && 'Dashboard Overview'}
            {activeTab === 'orders' && 'Order Management'}
            {activeTab === 'payouts' && 'Payout History'}
            {activeTab === 'service-fees' && 'Service Fee Settings'}
            {activeTab === 'vendors' && 'Vendor Management'}
            {activeTab === 'pending-vendors' && 'Pending Vendor Approvals'}
            {activeTab === 'riders' && 'Rider Management'}
            {activeTab === 'pending-riders' && 'Pending Rider Approvals'}
            {activeTab === 'customers' && 'Customer Management'}
            {activeTab === 'categories' && 'Category Management'}
            {activeTab === 'complaints' && 'Customer Complaints'}
            {activeTab === 'suggestions' && 'Customer Suggestions'}
          </h1>

          <div className="admin-user">
            <div className="admin-user-avatar">{userInitial}</div>
            <div className="admin-user-info">
              <div className="admin-user-name">{user?.name || 'Admin User'}</div>
              <div className="admin-user-role">Administrator</div>
            </div>
          </div>
        </div>

        {error && (
          <div className="error-message">
            <FaExclamationTriangle className="error-icon" />
            <span>{error}</span>
          </div>
        )}

        <div className="tab-content">
          {activeTab === 'dashboard' && dashboardData && (
          <div className="dashboard-tab">

            <div className="dashboard-stats">
              <div className="stat-card orders">
                <div className="stat-icon">
                  <FaShoppingCart />
                </div>
                <div className="stat-content">
                  <h3>Orders</h3>
                  <p className="stat-value">{dashboardData.counts?.total_orders || 0}</p>
                </div>
              </div>
              <div className="stat-card customers">
                <div className="stat-icon">
                  <FaUsers />
                </div>
                <div className="stat-content">
                  <h3>Customers</h3>
                  <p className="stat-value">{dashboardData.counts?.total_customers || 0}</p>
                </div>
              </div>
              <div className="stat-card vendors">
                <div className="stat-icon">
                  <FaStore />
                </div>
                <div className="stat-content">
                  <h3>Vendors</h3>
                  <p className="stat-value">{dashboardData.counts?.total_vendors || 0}</p>
                </div>
              </div>
              <div className="stat-card vendors">
                <div className="stat-icon">
                  <FaUserCheck />
                </div>
                <div className="stat-content">
                  <h3>Pending Vendors</h3>
                  <p className="stat-value">{dashboardData.counts?.pending_vendors || 0}</p>
                </div>
              </div>
              <div className="stat-card riders">
                <div className="stat-icon">
                  <FaMotorcycle />
                </div>
                <div className="stat-content">
                  <h3>Riders</h3>
                  <p className="stat-value">{dashboardData.counts?.total_riders || 0}</p>
                </div>
              </div>
              <div className="stat-card riders">
                <div className="stat-icon">
                  <FaClock />
                </div>
                <div className="stat-content">
                  <h3>Pending Riders</h3>
                  <p className="stat-value">{dashboardData.counts?.pending_riders || 0}</p>
                </div>
              </div>
              <div className="stat-card suggestions">
                <div className="stat-icon">
                  <FaLightbulb />
                </div>
                <div className="stat-content">
                  <h3>Total Suggestions</h3>
                  <p className="stat-value">{dashboardData.counts?.total_suggestions || 0}</p>
                </div>
              </div>
              <div className="stat-card suggestions">
                <div className="stat-icon">
                  <FaExclamationTriangle />
                </div>
                <div className="stat-content">
                  <h3>Pending Suggestions</h3>
                  <p className="stat-value">{dashboardData.counts?.pending_suggestions || 0}</p>
                </div>
              </div>
              <div className="stat-card complaints">
                <div className="stat-icon">
                  <FaCommentAlt />
                </div>
                <div className="stat-content">
                  <h3>Total Complaints</h3>
                  <p className="stat-value">{dashboardData.counts?.total_complaints || 0}</p>
                </div>
              </div>
              <div className="stat-card complaints">
                <div className="stat-icon">
                  <FaExclamationTriangle />
                </div>
                <div className="stat-content">
                  <h3>Pending Complaints</h3>
                  <p className="stat-value">{dashboardData.counts?.pending_complaints || 0}</p>
                </div>
              </div>
              <div className="stat-card revenue">
                <div className="stat-icon">
                  <FaMoneyBillWave />
                </div>
                <div className="stat-content">
                  <h3>Total Revenue</h3>
                  <p className="stat-value">
                    GH₵
                    {dashboardData.revenue_stats?.total_revenue || 0}
                  </p>
                </div>
              </div>
              <div className="stat-card revenue">
                <div className="stat-icon">
                  <FaMoneyBillWave />
                </div>
                <div className="stat-content">
                  <h3>Monthly Revenue</h3>
                  <div className="monthly-details">
                    {dashboardData.monthly_revenue?.map((item) => (
                      <div key={`monthly-revenue-${item.month}`} className="monthly-item">
                        <span className="month">
                          {item.month}
                          :
                        </span>
                        <span className="value">
                          GH₵
                          {item.total_revenue}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              <div className="stat-card orders">
                <div className="stat-icon">
                  <FaShoppingCart />
                </div>
                <div className="stat-content">
                  <h3>Monthly Orders</h3>
                  <div className="monthly-details">
                    {dashboardData.monthly_orders?.map((item) => (
                      <div key={`monthly-order-${item.month}`} className="monthly-item">
                        <span className="month">
                          {item.month}
                          :
                        </span>
                        <span className="value">{item.order_count}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              <div className="stat-card fees">
                <div className="stat-icon">
                  <FaPercentage />
                </div>
                <div className="stat-content">
                  <h3>Total Service Fees</h3>
                  <p className="stat-value">
                    GH₵
                    {dashboardData.revenue_stats?.total_service_fees || 0}
                  </p>
                </div>
              </div>
              <div className="stat-card fees">
                <div className="stat-icon">
                  <FaStore />
                </div>
                <div className="stat-content">
                  <h3>Vendor Service Fees</h3>
                  <p className="stat-value">
                    GH₵
                    {dashboardData.revenue_stats?.vendor_service_fees || 0}
                  </p>
                </div>
              </div>
              <div className="stat-card fees">
                <div className="stat-icon">
                  <FaMotorcycle />
                </div>
                <div className="stat-content">
                  <h3>Rider Service Fees</h3>
                  <p className="stat-value">
                    GH₵
                    {dashboardData.revenue_stats?.rider_service_fees || 0}
                  </p>
                </div>
              </div>
            </div>

            <div className="dashboard-alerts">
              <h3 className="dashboard-section-title">
                <FaBell className="dashboard-section-icon" />
                Alerts
              </h3>
              {dashboardAlerts.map((alert) => (
                <div key={alert.id} className="alert-item">
                  <div className="alert-content">
                    <div className="alert-icon">
                      {alert.icon}
                    </div>
                    <p className="alert-text">
                      <strong>{alert.count}</strong>
                      {' '}
                      {alert.text}
                    </p>
                  </div>
                  <button
                    type="button"
                    onClick={() => dispatch(setActiveTab(alert.tab))}
                    className="view-button"
                  >
                    <FaEye />
                    View
                  </button>
                </div>
              ))}
            </div>

            <div className="dashboard-statistics">
              <div className="statistics-section orders-statistics">
                <div className="statistics-header">
                  <h3 className="statistics-title">
                    <FaShoppingCart className="statistics-icon" />
                    Orders Statistics
                  </h3>
                </div>
                <div className="statistics-content">
                  <div className="orders-status-container">
                    <h4 className="statistics-section-title">Orders by Status</h4>
                    <div className="status-stats">
                      {dashboardData.orders_by_status && (
                        <div className="status-stats-grid">
                          <div className="status-stat-item canceled">
                            <span className="status-name">Canceled</span>
                            <span className="status-count">{dashboardData.orders_by_status.canceled || 0}</span>
                          </div>
                          <div className="status-stat-item confirmed">
                            <span className="status-name">Confirmed</span>
                            <span className="status-count">{dashboardData.orders_by_status.confirmed || 0}</span>
                          </div>
                          <div className="status-stat-item delivered">
                            <span className="status-name">Delivered</span>
                            <span className="status-count">{dashboardData.orders_by_status.delivered || 0}</span>
                          </div>
                          <div className="status-stat-item out-for-delivery">
                            <span className="status-name">Out for Delivery</span>
                            <span className="status-count">{dashboardData.orders_by_status.out_for_delivery || 0}</span>
                          </div>
                          <div className="status-stat-item pending">
                            <span className="status-name">Pending</span>
                            <span className="status-count">{dashboardData.orders_by_status.pending || 0}</span>
                          </div>
                          <div className="status-stat-item processing">
                            <span className="status-name">Processing</span>
                            <span className="status-count">{dashboardData.orders_by_status.processing || 0}</span>
                          </div>
                          <div className="status-stat-item ready-for-pickup">
                            <span className="status-name">Ready for Pickup</span>
                            <span className="status-count">{dashboardData.orders_by_status.ready_for_pickup || 0}</span>
                          </div>
                          <div className="status-stat-item received">
                            <span className="status-name">Received</span>
                            <span className="status-count">{dashboardData.orders_by_status.received || 0}</span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="dashboard-statistics">
              <div className="statistics-section complaints-suggestions-statistics">
                <div className="statistics-header">
                  <h3 className="statistics-title">
                    <FaCommentAlt className="statistics-icon" />
                    Complaints & Suggestions
                  </h3>
                </div>
                <div className="statistics-content">
                  <div className="complaints-suggestions-container">
                    {/* Suggestions Section */}
                    <div className="suggestions-section">
                      <h4 className="statistics-section-title">Suggestions</h4>

                      {/* Suggestions by Category */}
                      <div className="statistics-subsection">
                        <h5 className="statistics-subsection-title">By Category</h5>
                        <div className="category-stats">
                          {suggestionStatistics && suggestionStatistics.suggestions_by_category && (
                            <div className="category-stats-grid">
                              <div className="category-stat-item">
                                <span className="category-name">App</span>
                                <span className="category-count">{suggestionStatistics.suggestions_by_category.app || 0}</span>
                              </div>
                              <div className="category-stat-item">
                                <span className="category-name">Delivery</span>
                                <span className="category-count">{suggestionStatistics.suggestions_by_category.delivery || 0}</span>
                              </div>
                              <div className="category-stat-item">
                                <span className="category-name">Other</span>
                                <span className="category-count">{suggestionStatistics.suggestions_by_category.other || 0}</span>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Suggestions by Status */}
                      <div className="statistics-subsection">
                        <h5 className="statistics-subsection-title">By Status</h5>
                        <div className="status-stats">
                          {suggestionStatistics && suggestionStatistics.suggestions_by_status && (
                            <div className="status-stats-grid">
                              <div className="status-stat-item implemented">
                                <span className="status-name">Implemented</span>
                                <span className="status-count">{suggestionStatistics.suggestions_by_status.implemented || 0}</span>
                              </div>
                              <div className="status-stat-item pending">
                                <span className="status-name">Pending</span>
                                <span className="status-count">{suggestionStatistics.suggestions_by_status.pending || 0}</span>
                              </div>
                              <div className="status-stat-item under-review">
                                <span className="status-name">Under Review</span>
                                <span className="status-count">{suggestionStatistics.suggestions_by_status.under_review || 0}</span>
                              </div>
                              <div className="status-stat-item rejected">
                                <span className="status-name">Rejected</span>
                                <span className="status-count">{suggestionStatistics.suggestions_by_status.rejected || 0}</span>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Complaints Section */}
                    <div className="complaints-section">
                      <h4 className="statistics-section-title">Complaints</h4>

                      {/* Complaints by Category */}
                      <div className="statistics-subsection">
                        <h5 className="statistics-subsection-title">By Category</h5>
                        <div className="category-stats">
                          {complaintStatistics && complaintStatistics.complaints_by_category && (
                            <div className="category-stats-grid">
                              <div className="category-stat-item">
                                <span className="category-name">App</span>
                                <span className="category-count">{complaintStatistics.complaints_by_category.app || 0}</span>
                              </div>
                              <div className="category-stat-item">
                                <span className="category-name">Delivery</span>
                                <span className="category-count">{complaintStatistics.complaints_by_category.delivery || 0}</span>
                              </div>
                              <div className="category-stat-item">
                                <span className="category-name">Other</span>
                                <span className="category-count">{complaintStatistics.complaints_by_category.other || 0}</span>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Complaints by Status */}
                      <div className="statistics-subsection">
                        <h5 className="statistics-subsection-title">By Status</h5>
                        <div className="status-stats">
                          {complaintStatistics && complaintStatistics.complaints_by_status && (
                            <div className="status-stats-grid">
                              <div className="status-stat-item rejected">
                                <span className="status-name">Rejected</span>
                                <span className="status-count">{complaintStatistics.complaints_by_status.rejected || 0}</span>
                              </div>
                              <div className="status-stat-item resolved">
                                <span className="status-name">Resolved</span>
                                <span className="status-count">{complaintStatistics.complaints_by_status.resolved || 0}</span>
                              </div>
                              <div className="status-stat-item under-review">
                                <span className="status-name">Under Review</span>
                                <span className="status-count">{complaintStatistics.complaints_by_status.under_review || 0}</span>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="dashboard-statistics performance-statistics">
              <div className="statistics-section vendor-sales-statistics">
                <div className="statistics-header">
                  <h3 className="statistics-title">
                    <FaStore className="statistics-icon" />
                    Top Vendors by Sales
                  </h3>
                </div>
                {dashboardData.top_vendors_by_sales && dashboardData
                  .top_vendors_by_sales.length > 0 ? (
                    <div className="statistics-content">
                      {dashboardData.top_vendors_by_sales.map((monthData) => (
                        <div key={`vendor-month-${monthData.month}`} className="statistics-month-container">
                          <h4 className="statistics-month-title">{monthData.month}</h4>
                          <div className="admin-table-container">
                            <table className="admin-table">
                              <thead>
                                <tr>
                                  <th>Vendor</th>
                                  <th>Total Sales</th>
                                  <th>Orders</th>
                                </tr>
                              </thead>
                              <tbody>
                                {monthData.vendors && monthData.vendors
                                  .map((vendor) => (
                                    <tr key={`vendor-${vendor.vendor_id}-${monthData.month}`}>
                                      <td>{vendor.vendor_name}</td>
                                      <td className="price-cell">
                                        <span className="currency">GH₵</span>
                                        <span className="amount">{vendor.total_sales}</span>
                                      </td>
                                      <td>{vendor.order_count}</td>
                                    </tr>
                                  ))}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="no-data-message">No vendor sales data available</p>
                  )}
              </div>

              <div className="statistics-section vendor-statistics">
                <div className="statistics-header">
                  <h3 className="statistics-title">
                    <FaStore className="statistics-icon" />
                    Vendor Statistics
                  </h3>
                </div>
                {vendorStatistics ? (
                  <div className="statistics-content">
                    <div className="vendor-stats-grid">
                      {/* New Vendors This Month */}
                      <div className="vendor-stat-card">
                        <div className="vendor-stat-icon">
                          <FaUserPlus />
                        </div>
                        <div className="vendor-stat-info">
                          <h4>New Vendors This Month</h4>
                          <p className="vendor-stat-value">{vendorStatistics.new_vendors_this_month || 0}</p>
                        </div>
                      </div>

                      {/* Vendors by Status */}
                      <div className="vendor-status-section">
                        <h4 className="statistics-section-title">Vendors by Status</h4>
                        <div className="status-stats">
                          <div className="status-stats-grid">
                            <div className="status-stat-item active">
                              <span className="status-name">Verified</span>
                              <span className="status-count">
                                {vendorStatistics.vendors_by_status?.verified || 0}
                              </span>
                            </div>
                            <div className="status-stat-item pending">
                              <span className="status-name">Pending</span>
                              <span className="status-count">
                                {vendorStatistics.vendors_by_status?.pending || 0}
                              </span>
                            </div>
                            <div className="status-stat-item rejected">
                              <span className="status-name">Rejected</span>
                              <span className="status-count">
                                {vendorStatistics.vendors_by_status?.rejected || 0}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Top Vendors by Revenue */}
                    <div className="top-vendors-revenue-section">
                      <h4 className="statistics-section-title">Top Vendors by Revenue</h4>
                      {vendorStatistics.top_vendors_by_revenue
                       && vendorStatistics.top_vendors_by_revenue.length > 0 ? (
                         <div className="admin-table-container">
                           <table className="admin-table">
                             <thead>
                               <tr>
                                 <th>Vendor</th>
                                 <th>Orders</th>
                                 <th>Revenue</th>
                               </tr>
                             </thead>
                             <tbody>
                               {vendorStatistics.top_vendors_by_revenue.map((vendor) => (
                                 <tr key={`revenue-vendor-${vendor.id}`}>
                                   <td>{vendor.name}</td>
                                   <td>{vendor.order_count || 0}</td>
                                   <td className="price-cell">
                                     <span className="currency">GH₵</span>
                                     <span className="amount">{vendor.total_revenue || 0}</span>
                                   </td>
                                 </tr>
                               ))}
                             </tbody>
                           </table>
                         </div>
                        ) : (
                          <div className="no-data-container">
                            <p className="no-data-message">No top vendors data available</p>
                            <div className="empty-table-placeholder">
                              <table className="admin-table">
                                <thead>
                                  <tr>
                                    <th>Vendor</th>
                                    <th>Orders</th>
                                    <th>Revenue</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr>
                                    <td colSpan="3" className="empty-data-cell">No data available</td>
                                  </tr>
                                </tbody>
                              </table>
                            </div>
                          </div>
                        )}
                    </div>
                  </div>
                ) : (
                  <p className="no-data-message">No vendor statistics available</p>
                )}
              </div>

              <div className="statistics-section rider-deliveries-statistics">
                <div className="statistics-header">
                  <h3 className="statistics-title">
                    <FaMotorcycle className="statistics-icon" />
                    Top Riders by Deliveries
                  </h3>
                </div>
                {dashboardData.top_riders_by_deliveries && dashboardData
                  .top_riders_by_deliveries.length > 0 ? (
                    <div className="statistics-content">
                      {dashboardData.top_riders_by_deliveries.map((monthData) => (
                        <div key={`rider-month-${monthData.month}`} className="statistics-month-container">
                          <h4 className="statistics-month-title">{monthData.month}</h4>
                          <div className="admin-table-container">
                            <table className="admin-table">
                              <thead>
                                <tr>
                                  <th>Rider</th>
                                  <th>Deliveries</th>
                                  <th>Earnings</th>
                                </tr>
                              </thead>
                              <tbody>
                                {monthData.riders && monthData.riders.map((rider) => (
                                  <tr key={`rider-${rider.rider_id}-${monthData.month}`}>
                                    <td>{rider.rider_name}</td>
                                    <td>{rider.delivery_count}</td>
                                    <td className="price-cell">
                                      <span className="currency">GH₵</span>
                                      <span className="amount">{rider.total_earnings}</span>
                                    </td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="no-data-message">No rider deliveries data available</p>
                  )}
              </div>

              <div className="statistics-section rider-statistics">
                <div className="statistics-header">
                  <h3 className="statistics-title">
                    <FaMotorcycle className="statistics-icon" />
                    Rider Statistics
                  </h3>
                </div>
                {riderStatistics ? (
                  <div className="statistics-content">
                    <div className="rider-stats-grid">
                      {/* New Riders This Month */}
                      <div className="rider-stat-card">
                        <div className="rider-stat-icon">
                          <FaUserPlus />
                        </div>
                        <div className="rider-stat-info">
                          <h4>New Riders This Month</h4>
                          <p className="rider-stat-value">{riderStatistics.new_riders_this_month || 0}</p>
                        </div>
                      </div>

                      {/* Riders by Status */}
                      <div className="rider-status-section">
                        <h4 className="statistics-section-title">Riders by Status</h4>
                        <div className="status-stats">
                          <div className="status-stats-grid">
                            <div className="status-stat-item active">
                              <span className="status-name">Verified</span>
                              <span className="status-count">
                                {riderStatistics.riders_by_status?.verified || 0}
                              </span>
                            </div>
                            <div className="status-stat-item pending">
                              <span className="status-name">Pending</span>
                              <span className="status-count">
                                {riderStatistics.riders_by_status?.pending || 0}
                              </span>
                            </div>
                            <div className="status-stat-item rejected">
                              <span className="status-name">Rejected</span>
                              <span className="status-count">
                                {riderStatistics.riders_by_status?.rejected || 0}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Top Riders by Revenue */}
                    <div className="top-riders-revenue-section">
                      <h4 className="statistics-section-title">Top Riders by Revenue</h4>
                      {riderStatistics.top_riders_by_revenue && riderStatistics
                        .top_riders_by_revenue.length > 0 ? (
                          <div className="admin-table-container">
                            <table className="admin-table">
                              <thead>
                                <tr>
                                  <th>Rider</th>
                                  <th>Deliveries</th>
                                  <th>Revenue</th>
                                </tr>
                              </thead>
                              <tbody>
                                {riderStatistics.top_riders_by_revenue.map((rider) => (
                                  <tr key={`revenue-rider-${rider.id}`}>
                                    <td>{rider.name}</td>
                                    <td>{rider.delivery_count}</td>
                                    <td className="price-cell">
                                      <span className="currency">GH₵</span>
                                      <span className="amount">{rider.total_revenue}</span>
                                    </td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        ) : (
                          <div className="no-data-container">
                            <p className="no-data-message">No top riders data available</p>
                            <div className="empty-table-placeholder">
                              <table className="admin-table">
                                <thead>
                                  <tr>
                                    <th>Rider</th>
                                    <th>Deliveries</th>
                                    <th>Revenue</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr>
                                    <td colSpan="3" className="empty-data-cell">No data available</td>
                                  </tr>
                                </tbody>
                              </table>
                            </div>
                          </div>
                        )}
                    </div>
                  </div>
                ) : (
                  <p className="no-data-message">No rider statistics available</p>
                )}
              </div>
            </div>

            <div className="dashboard-statistics customer-statistics-container">
              <div className="statistics-section customer-statistics">
                <div className="statistics-header">
                  <h3 className="statistics-title">
                    <FaUsers className="statistics-icon" />
                    Customer Statistics
                  </h3>
                  <button
                    type="button"
                    className="view-all"
                    onClick={() => dispatch(setActiveTab('customers'))}
                  >
                    View All Customers
                  </button>
                </div>
                {!customerStatistics ? (
                  <p>No customer statistics available</p>
                ) : (
                  <div className="customer-statistics-content">
                    <div className="customer-stats-grid">
                      <div className="customer-stat-card">
                        <div className="customer-stat-icon">
                          <FaUsers />
                        </div>
                        <div className="customer-stat-info">
                          <h4>Total Customers</h4>
                          <p className="customer-stat-value">{customerStatistics.total_customers || 0}</p>
                        </div>
                      </div>

                      <div className="customer-stat-card">
                        <div className="customer-stat-icon">
                          <FaUserPlus />
                        </div>
                        <div className="customer-stat-info">
                          <h4>New Customers This Month</h4>
                          <p className="customer-stat-value">{customerStatistics.new_customers_this_month || 0}</p>
                        </div>
                      </div>
                    </div>

                    <div className="customer-statistics-sections">
                      {customerStatistics.monthly_signups && customerStatistics
                        .monthly_signups.length > 0 && (
                        <div className="monthly-signups-section">
                          <h4 className="statistics-section-title">Monthly new customer Signups</h4>
                          <div className="admin-table-container">
                            <table className="admin-table">
                              <thead>
                                <tr>
                                  <th>Month</th>
                                  <th>New Signups</th>
                                </tr>
                              </thead>
                              <tbody>
                                {customerStatistics.monthly_signups.map((monthData) => (
                                  <tr key={`month-signup-${monthData.month}`}>
                                    <td>{monthData.month}</td>
                                    <td>{monthData.signup_count}</td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      )}

                      {customerStatistics.top_customers_by_orders && customerStatistics
                        .top_customers_by_orders.length > 0 && (
                        <div className="top-customers-section">
                          <h4 className="statistics-section-title">Top Customers by Orders</h4>
                          <div className="admin-table-container">
                            <table className="admin-table">
                              <thead>
                                <tr>
                                  <th>ID</th>
                                  <th>Customer</th>
                                  <th>Orders</th>
                                </tr>
                              </thead>
                              <tbody>
                                {customerStatistics.top_customers_by_orders
                                  .map((customer) => (
                                    <tr key={`order-${customer.id}`}>
                                      <td>{customer.id}</td>
                                      <td>{customer.name}</td>
                                      <td>{customer.order_count}</td>
                                    </tr>
                                  ))}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      )}

                      {customerStatistics.top_customers_by_spending && customerStatistics
                        .top_customers_by_spending.length > 0 && (
                        <div className="top-customers-section">
                          <h4 className="statistics-section-title">Top Customers by Spending</h4>
                          <div className="admin-table-container">
                            <table className="admin-table">
                              <thead>
                                <tr>
                                  <th>ID</th>
                                  <th>Customer</th>
                                  <th>Total Spent</th>
                                </tr>
                              </thead>
                              <tbody>
                                {customerStatistics.top_customers_by_spending
                                  .map((customer) => (
                                    <tr key={`spend-${customer.id}`}>
                                      <td>{customer.id}</td>
                                      <td>{customer.name}</td>
                                      <td className="price-cell">
                                        <span className="currency">GH₵</span>
                                        <span className="amount">{customer.total_spent}</span>
                                      </td>
                                    </tr>
                                  ))}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="dashboard-recent">
              <div className="recent-orders">
                <div className="recent-header">
                  <h3 className="recent-title">
                    <FaShoppingCart className="recent-icon" />
                    Recent Orders
                  </h3>
                  <button
                    type="button"
                    className="view-all"
                    onClick={() => dispatch(setActiveTab('orders'))}
                  >
                    View All
                  </button>
                </div>
                {dashboardData.recent_orders?.length === 0 ? (
                  <p>No recent orders</p>
                ) : (
                  <div className="admin-table-container">
                    <table className="admin-table">
                      <thead>
                        <tr>
                          <th>Order ID</th>
                          <th>Customer</th>
                          <th>Status</th>
                          <th>Amount</th>
                          <th>Date</th>
                        </tr>
                      </thead>
                      <tbody>
                        {dashboardData.recent_orders?.map((order) => (
                          <tr key={order.id}>
                            <td>{order.id}</td>
                            <td>{order.customer_name}</td>
                            <td>
                              {order.status ? (
                                <span className={`table-status status-${order.status.toLowerCase()}`}>
                                  {order.status}
                                </span>
                              ) : (
                                <span className="table-status status-unknown">
                                  Unknown
                                </span>
                              )}
                            </td>
                            <td className="price-cell">
                              <span className="currency">GH₵</span>
                              <span className="amount">{order.total_price}</span>
                            </td>
                            <td>{new Date(order.created_at).toLocaleString()}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>

              <div className="recent-payouts">
                <div className="recent-header">
                  <h3 className="recent-title">
                    <FaMoneyBillWave className="recent-icon" />
                    Recent Payouts
                  </h3>
                  <button
                    type="button"
                    className="view-all"
                    onClick={() => dispatch(setActiveTab('payouts'))}
                  >
                    View All
                  </button>
                </div>
                {dashboardData.recent_payouts?.length === 0 ? (
                  <p>No recent payouts</p>
                ) : (
                  <div className="admin-table-container">
                    <table className="admin-table">
                      <thead>
                        <tr>
                          <th>ID</th>
                          <th>Recipient</th>
                          <th>Type</th>
                          <th>Amount</th>
                          <th>Status</th>
                        </tr>
                      </thead>
                      <tbody>
                        {dashboardData.recent_payouts?.map((payout) => (
                          <tr key={payout.id}>
                            <td>{payout.id}</td>
                            <td>{payout.recipient_name}</td>
                            <td>{payout.recipient_type}</td>
                            <td className="price-cell">
                              <span className="currency">GH₵</span>
                              <span className="amount">{payout.amount}</span>
                            </td>
                            <td>
                              {payout.status ? (
                                <span className={`table-status status-${payout.status.toLowerCase()}`}>
                                  {payout.status}
                                </span>
                              ) : (
                                <span className="table-status status-unknown">
                                  Unknown
                                </span>
                              )}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>

              <div className="recent-complaints">
                <div className="recent-header">
                  <h3 className="recent-title">
                    <FaCommentAlt className="recent-icon" />
                    Recent Complaints
                  </h3>
                  <button
                    type="button"
                    className="view-all"
                    onClick={() => dispatch(setActiveTab('complaints'))}
                  >
                    View All
                  </button>
                </div>
                {dashboardData.recent_complaints?.length === 0 ? (
                  <p>No recent complaints</p>
                ) : (
                  <div className="admin-table-container">
                    <table className="admin-table">
                      <thead>
                        <tr>
                          <th>ID</th>
                          <th>Customer</th>
                          <th>Subject</th>
                          <th>Category</th>
                          <th>Status</th>
                          <th>Date</th>
                        </tr>
                      </thead>
                      <tbody>
                        {dashboardData.recent_complaints?.map((complaint) => (
                          <tr key={complaint.id}>
                            <td>{complaint.id}</td>
                            <td>{complaint.customer_name}</td>
                            <td>{complaint.complaint_summary}</td>
                            <td>{complaint.category}</td>
                            <td>
                              {complaint.status ? (
                                <span className={`table-status status-${complaint.status.toLowerCase()}`}>
                                  {complaint.status}
                                </span>
                              ) : (
                                <span className="table-status status-unknown">
                                  Unknown
                                </span>
                              )}
                            </td>
                            <td>{new Date(complaint.created_at).toLocaleString()}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>

              <div className="recent-suggestions">
                <div className="recent-header">
                  <h3 className="recent-title">
                    <FaLightbulb className="recent-icon" />
                    Recent Suggestions
                  </h3>
                  <button
                    type="button"
                    className="view-all"
                    onClick={() => dispatch(setActiveTab('suggestions'))}
                  >
                    View All
                  </button>
                </div>
                {dashboardData.recent_suggestions?.length === 0 ? (
                  <p>No recent suggestions</p>
                ) : (
                  <div className="admin-table-container">
                    <table className="admin-table">
                      <thead>
                        <tr>
                          <th>ID</th>
                          <th>Customer</th>
                          <th>Ratings</th>
                          <th>Subject</th>
                          <th>Category</th>
                          <th>Status</th>
                          <th>Date</th>
                        </tr>
                      </thead>
                      <tbody>
                        {dashboardData.recent_suggestions?.map((suggestion) => (
                          <tr key={suggestion.id}>
                            <td>{suggestion.id}</td>
                            <td>{suggestion.customer_name}</td>
                            <td>{suggestion.rating}</td>
                            <td>{suggestion.suggestion_summary}</td>
                            <td>{suggestion.category}</td>
                            <td>
                              {suggestion.status ? (
                                <span className={`table-status status-${suggestion.status.toLowerCase()}`}>
                                  {suggestion.status}
                                </span>
                              ) : (
                                <span className="table-status status-unknown">
                                  Unknown
                                </span>
                              )}
                            </td>
                            <td>{new Date(suggestion.created_at).toLocaleString()}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            </div>
          </div>
          )}

          {activeTab === 'orders' && (
          <div className="orders-tab">
            <h2>All Orders</h2>
            {orders.length === 0 ? (
              <p>No orders found.</p>
            ) : (
              <div className="admin-table-container">
                <table className="admin-table">
                  <thead>
                    <tr>
                      <th>Order ID</th>
                      <th>Customer</th>
                      <th>Vendor</th>
                      <th>Rider</th>
                      <th>Total Amount</th>
                      <th>Status</th>
                      <th>Created At</th>
                    </tr>
                  </thead>
                  <tbody>
                    {orders.map((order) => (
                      <tr key={order.id}>
                        <td>{order.id}</td>
                        <td>{order.customer?.name || 'N/A'}</td>
                        <td>{order.vendor?.name || 'N/A'}</td>
                        <td>{order.rider?.name || 'N/A'}</td>
                        <td className="price-cell">
                          <span className="currency">GH₵</span>
                          <span className="amount">{order.total_price}</span>
                        </td>
                        <td>
                          {order.status ? (
                            <span className={`table-status status-${order.status.toLowerCase()}`}>
                              {order.status}
                            </span>
                          ) : (
                            <span className="table-status status-unknown">
                              Unknown
                            </span>
                          )}
                        </td>
                        <td>{new Date(order.created_at).toLocaleString()}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
          )}

          {activeTab === 'payouts' && (
          <div className="payouts-tab">
            <h2>All Payouts</h2>
            {payouts.length === 0 ? (
              <p>No payouts found.</p>
            ) : (
              <div className="admin-table-container">
                <table className="admin-table">
                  <thead>
                    <tr>
                      <th>Payout ID</th>
                      <th>Recipient</th>
                      <th>Recipient Type</th>
                      <th>Amount</th>
                      <th>Status</th>
                      <th>Order ID</th>
                      <th>Created At</th>
                    </tr>
                  </thead>
                  <tbody>
                    {payouts.map((payout) => (
                      <tr key={payout.id}>
                        <td>{payout.id}</td>
                        <td>{payout.recipient_name}</td>
                        <td>{payout.recipient_type}</td>
                        <td className="price-cell">
                          <span className="currency">GH₵</span>
                          <span className="amount">{payout.amount}</span>
                        </td>
                        <td>
                          {payout.status ? (
                            <span className={`table-status status-${payout.status.toLowerCase()}`}>
                              {payout.status}
                            </span>
                          ) : (
                            <span className="table-status status-unknown">
                              Unknown
                            </span>
                          )}
                        </td>
                        <td>{payout.order_id}</td>
                        <td>{new Date(payout.created_at).toLocaleString()}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
          )}

          {activeTab === 'service-fees' && (
          <div className="service-fees-tab">
            <h2 className="dashboard-section-title">
              <FaPercentage className="dashboard-section-icon" />
              Service Fees Configuration
            </h2>

            <div className="fee-info">
              <FaInfoCircle />
              <span>
                These percentages determine how revenue is distributed
                between vendors, riders, and the platform.
              </span>
            </div>

            <div className="service-fee-form">
              <div className="form-group">
                <label htmlFor="vendorFeePercentage">
                  Vendor Fee Percentage
                  <input
                    type="number"
                    id="vendorFeePercentage"
                    name="vendorFeePercentage"
                    value={serviceFees.vendorFeePercentage}
                    onChange={handleServiceFeeChange}
                    min="0"
                    max="100"
                    step="0.1"
                  />
                </label>
              </div>

              <div className="form-group">
                <label htmlFor="riderFeePercentage">
                  Rider Fee Percentage
                  <input
                    type="number"
                    id="riderFeePercentage"
                    name="riderFeePercentage"
                    value={serviceFees.riderFeePercentage}
                    onChange={handleServiceFeeChange}
                    min="0"
                    max="100"
                    step="0.1"
                  />
                </label>
              </div>

              <div className="form-group">
                <label htmlFor="platformFeePercentage">
                  Platform Fee Percentage
                  <input
                    type="number"
                    id="platformFeePercentage"
                    name="platformFeePercentage"
                    value={serviceFees.platformFeePercentage}
                    onChange={handleServiceFeeChange}
                    min="0"
                    max="100"
                    step="0.1"
                  />
                </label>
              </div>

              <button
                type="button"
                className="update-fees-button"
                onClick={() => dispatch(updateServiceFees(serviceFees))}
                disabled={status === 'loading'}
              >
                {status === 'loading' ? (
                  <>
                    <FaSpinner className="fa-spin" />
                    Updating...
                  </>
                ) : (
                  <>
                    <FaCheck />
                    Update Service Fees
                  </>
                )}
              </button>
            </div>
          </div>
          )}

          {activeTab === 'vendors' && (
          <div className="vendors-tab">
            <h2 className="dashboard-section-title">
              <FaStore className="dashboard-section-icon" />
              Vendor Management
            </h2>
            {vendors.length === 0 ? (
              <div className="empty-state">
                <FaStore className="empty-icon" />
                <p>No vendors found in the system</p>
              </div>
            ) : (
              <div className="admin-table-container">
                <div className="table-header-actions">
                  <div className="table-search">
                    <input
                      type="text"
                      placeholder="Search vendors..."
                      className="search-input"
                    />
                  </div>
                </div>
                <table className="admin-table">
                  <thead>
                    <tr>
                      <th>ID</th>
                      <th>Name</th>
                      <th>Email</th>
                      <th>Phone</th>
                      <th>Address</th>
                      <th>Status</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {vendors.map((vendor) => (
                      <tr key={vendor.id}>
                        <td>{vendor.id}</td>
                        <td>{vendor.name}</td>
                        <td>{vendor.email}</td>
                        <td>{vendor.phone}</td>
                        <td>{vendor.address}</td>
                        <td>
                          {vendor.validation_status ? (
                            <span className={`table-status status-${vendor.validation_status.toLowerCase().replace('-', '_')}`}>
                              {vendor.validation_status.replace('-', ' ')}
                            </span>
                          ) : (
                            <span className="table-status status-unknown">
                              Unknown
                            </span>
                          )}
                        </td>
                        <td>
                          {vendor.validation_status === 'not-verified' && (
                            <>
                              <button
                                type="button"
                                onClick={() => handleApproveVendor(vendor.id)}
                                className="approve-button"
                              >
                                <FaCheck />
                                <span>Approve</span>
                              </button>
                              <button
                                type="button"
                                onClick={() => handleRejectVendor(vendor.id)}
                                className="reject-button"
                              >
                                <FaTimes />
                                <span>Reject</span>
                              </button>
                            </>
                          )}
                          {vendor.validation_status === 'rejected' && (
                            <button
                              type="button"
                              onClick={() => handleApproveVendor(vendor.id)}
                              className="approve-button"
                            >
                              <FaCheck />
                              <span>Approve</span>
                            </button>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
          )}

          {activeTab === 'pending-vendors' && (
          <div className="pending-vendors-tab">
            <h2 className="dashboard-section-title">
              <FaUserCheck className="dashboard-section-icon" />
              Pending Vendors Approval
            </h2>

            {pendingVendors.length === 0 ? (
              <div className="empty-state">
                <FaStore className="empty-icon" />
                <p>No pending vendors to approve at this time</p>
              </div>
            ) : (
              <div className="pending-vendors-list">
                {pendingVendors.map((vendor) => (
                  <div key={vendor.id} className="pending-vendor-card">
                    <div className="pending-card-header">
                      <h3>{vendor.name}</h3>
                    </div>

                    <div className="pending-card-body">
                      <div className="vendor-details">
                        <div className="detail-item">
                          <span className="detail-label">Email:</span>
                          <span className="detail-value">{vendor.email}</span>
                        </div>
                        <div className="detail-item">
                          <span className="detail-label">Phone:</span>
                          <span className="detail-value">{vendor.phone}</span>
                        </div>
                        <div className="detail-item">
                          <span className="detail-label">Address:</span>
                          <span className="detail-value">{vendor.address}</span>
                        </div>
                      </div>

                      <div className="document-section">
                        <h4>Verification Documents</h4>
                        <div className="document-grid">
                          {vendor.front_ghana_card_url && (
                            <div className="document-item">
                              <p>Ghana Card (Front)</p>
                              <div className="document-image-container">
                                <img
                                  src={vendor.front_ghana_card_url}
                                  alt="Ghana Card Front"
                                />
                                <button
                                  type="button"
                                  className="document-expand-button"
                                  onClick={() => openDocumentModal(vendor.front_ghana_card_url, 'Ghana Card (Front)')}
                                  aria-label="View Ghana Card Front in full size"
                                >
                                  <FaExpand />
                                  <span className="sr-only">View full size</span>
                                </button>
                              </div>
                            </div>
                          )}
                          {vendor.back_ghana_card_url && (
                            <div className="document-item">
                              <p>Ghana Card (Back)</p>
                              <div className="document-image-container">
                                <img
                                  src={vendor.back_ghana_card_url}
                                  alt="Ghana Card Back"
                                />
                                <button
                                  type="button"
                                  className="document-expand-button"
                                  onClick={() => openDocumentModal(vendor.back_ghana_card_url, 'Ghana Card (Back)')}
                                  aria-label="View Ghana Card Back in full size"
                                >
                                  <FaExpand />
                                  <span className="sr-only">View full size</span>
                                </button>
                              </div>
                            </div>
                          )}
                          {vendor.health_certificate_url && (
                            <div className="document-item">
                              <p>Health Certificate</p>
                              <div className="document-image-container">
                                <img
                                  src={vendor.health_certificate_url}
                                  alt="Health Certificate"
                                />
                                <button
                                  type="button"
                                  className="document-expand-button"
                                  onClick={() => openDocumentModal(vendor.health_certificate_url, 'Health Certificate')}
                                  aria-label="View Health Certificate in full size"
                                >
                                  <FaExpand />
                                  <span className="sr-only">View full size</span>
                                </button>
                              </div>
                            </div>
                          )}
                          {vendor.food_certificate_url && (
                            <div className="document-item">
                              <p>Food Certificate</p>
                              <div className="document-image-container">
                                <img
                                  src={vendor.food_certificate_url}
                                  alt="Food Certificate"
                                />
                                <button
                                  type="button"
                                  className="document-expand-button"
                                  onClick={() => openDocumentModal(vendor.food_certificate_url, 'Food Certificate')}
                                  aria-label="View Food Certificate in full size"
                                >
                                  <FaExpand />
                                  <span className="sr-only">View full size</span>
                                </button>
                              </div>
                            </div>
                          )}
                          {vendor.operation_license_url && (
                            <div className="document-item">
                              <p>Operation License</p>
                              <div className="document-image-container">
                                <img
                                  src={vendor.operation_license_url}
                                  alt="Operation License"
                                />
                                <button
                                  type="button"
                                  className="document-expand-button"
                                  onClick={() => openDocumentModal(vendor.operation_license_url, 'Operation License')}
                                  aria-label="View Operation License in full size"
                                >
                                  <FaExpand />
                                  <span className="sr-only">View full size</span>
                                </button>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="vendor-actions">
                        <button
                          type="button"
                          onClick={() => handleApproveVendor(vendor.id)}
                          className="approve-button"
                        >
                          <FaCheck />
                          Approve
                        </button>
                        <button
                          type="button"
                          onClick={() => handleRejectVendor(vendor.id)}
                          className="reject-button"
                        >
                          <FaTimes />
                          Reject
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
          )}

          {activeTab === 'riders' && (
          <div className="riders-tab">
            <h2 className="dashboard-section-title">
              <FaMotorcycle className="dashboard-section-icon" />
              Rider Management
            </h2>
            {riders.length === 0 ? (
              <div className="empty-state">
                <FaMotorcycle className="empty-icon" />
                <p>No riders found in the system</p>
              </div>
            ) : (
              <div className="admin-table-container">
                <div className="table-header-actions">
                  <div className="table-search">
                    <input
                      type="text"
                      placeholder="Search riders..."
                      className="search-input"
                    />
                  </div>
                </div>
                <table className="admin-table">
                  <thead>
                    <tr>
                      <th>ID</th>
                      <th>Name</th>
                      <th>Email</th>
                      <th>Phone</th>
                      <th>Address</th>
                      <th>Status</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {riders.map((rider) => (
                      <tr key={rider.id}>
                        <td>{rider.id}</td>
                        <td>{rider.name}</td>
                        <td>{rider.email}</td>
                        <td>{rider.phone}</td>
                        <td>{rider.address}</td>
                        <td>
                          {rider.validation_status ? (
                            <span className={`table-status status-${rider.validation_status.toLowerCase().replace('-', '_')}`}>
                              {rider.validation_status.replace('-', ' ')}
                            </span>
                          ) : (
                            <span className="table-status status-unknown">
                              Unknown
                            </span>
                          )}
                        </td>
                        <td>
                          {rider.validation_status === 'not-verified' && (
                            <>
                              <button
                                type="button"
                                onClick={() => handleApproveRider(rider.id)}
                                className="approve-button"
                              >
                                <FaCheck />
                                <span>Approve</span>
                              </button>
                              <button
                                type="button"
                                onClick={() => handleRejectRider(rider.id)}
                                className="reject-button"
                              >
                                <FaTimes />
                                <span>Reject</span>
                              </button>
                            </>
                          )}
                          {rider.validation_status === 'rejected' && (
                            <button
                              type="button"
                              onClick={() => handleApproveRider(rider.id)}
                              className="approve-button"
                            >
                              <FaCheck />
                              <span>Approve</span>
                            </button>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
          )}

          {activeTab === 'pending-riders' && (
          <div className="pending-riders-tab">
            <h2 className="dashboard-section-title">
              <FaMotorcycle className="dashboard-section-icon" />
              Pending Riders Approval
            </h2>

            {pendingRiders.length === 0 ? (
              <div className="empty-state">
                <FaMotorcycle className="empty-icon" />
                <p>No pending riders to approve at this time</p>
              </div>
            ) : (
              <div className="pending-riders-list">
                {pendingRiders.map((rider) => (
                  <div key={rider.id} className="pending-rider-card">
                    <div className="pending-card-header">
                      <h3>{rider.name}</h3>
                    </div>

                    <div className="pending-card-body">
                      <div className="rider-details">
                        <div className="detail-item">
                          <span className="detail-label">Email:</span>
                          <span className="detail-value">{rider.email}</span>
                        </div>
                        <div className="detail-item">
                          <span className="detail-label">Phone:</span>
                          <span className="detail-value">{rider.phone}</span>
                        </div>
                        <div className="detail-item">
                          <span className="detail-label">Address:</span>
                          <span className="detail-value">{rider.address}</span>
                        </div>
                        <div className="detail-item">
                          <span className="detail-label">Within Dunkwa:</span>
                          <span className="detail-value">
                            GH₵
                            {rider.within_dunkwa_price}
                          </span>
                        </div>
                        <div className="detail-item">
                          <span className="detail-label">Outside Dunkwa:</span>
                          <span className="detail-value">
                            GH₵
                            {rider.outside_dunkwa_price}
                          </span>
                        </div>
                      </div>

                      <div className="document-section">
                        <h4>Verification Documents</h4>
                        <div className="document-grid">
                          {rider.avatar_url && (
                            <div className="document-item">
                              <p>Avatar</p>
                              <div className="document-image-container">
                                <img src={rider.avatar_url} alt="Avatar" />
                                <button
                                  type="button"
                                  className="document-expand-button"
                                  onClick={() => openDocumentModal(rider.avatar_url, 'Avatar')}
                                  aria-label="View Avatar in full size"
                                >
                                  <FaExpand />
                                  <span className="sr-only">View full size</span>
                                </button>
                              </div>
                            </div>
                          )}
                          {rider.front_drivers_license_url && (
                            <div className="document-item">
                              <p>Driver&apos;s License (Front)</p>
                              <div className="document-image-container">
                                <img src={rider.front_drivers_license_url} alt="Driver's License Front" />
                                <button
                                  type="button"
                                  className="document-expand-button"
                                  onClick={() => openDocumentModal(rider.front_drivers_license_url, 'Driver\'s License (Front)')}
                                  aria-label="View Driver's License Front in full size"
                                >
                                  <FaExpand />
                                  <span className="sr-only">View full size</span>
                                </button>
                              </div>
                            </div>
                          )}
                          {rider.back_drivers_license_url && (
                            <div className="document-item">
                              <p>Driver&apos;s License (Back)</p>
                              <div className="document-image-container">
                                <img src={rider.back_drivers_license_url} alt="Driver's License Back" />
                                <button
                                  type="button"
                                  className="document-expand-button"
                                  onClick={() => openDocumentModal(rider.back_drivers_license_url, 'Driver\'s License (Back)')}
                                  aria-label="View Driver's License Back in full size"
                                >
                                  <FaExpand />
                                  <span className="sr-only">View full size</span>
                                </button>
                              </div>
                            </div>
                          )}
                          {rider.front_ghana_card_url && (
                            <div className="document-item">
                              <p>Ghana Card (Front)</p>
                              <div className="document-image-container">
                                <img src={rider.front_ghana_card_url} alt="Ghana Card Front" />
                                <button
                                  type="button"
                                  className="document-expand-button"
                                  onClick={() => openDocumentModal(rider.front_ghana_card_url, 'Ghana Card (Front)')}
                                  aria-label="View Ghana Card Front in full size"
                                >
                                  <FaExpand />
                                  <span className="sr-only">View full size</span>
                                </button>
                              </div>
                            </div>
                          )}
                          {rider.back_ghana_card_url && (
                            <div className="document-item">
                              <p>Ghana Card (Back)</p>
                              <div className="document-image-container">
                                <img src={rider.back_ghana_card_url} alt="Ghana Card Back" />
                                <button
                                  type="button"
                                  className="document-expand-button"
                                  onClick={() => openDocumentModal(rider.back_ghana_card_url, 'Ghana Card (Back)')}
                                  aria-label="View Ghana Card Back in full size"
                                >
                                  <FaExpand />
                                  <span className="sr-only">View full size</span>
                                </button>
                              </div>
                            </div>
                          )}
                          {rider.moto_pic_url && (
                            <div className="document-item">
                              <p>Motorcycle</p>
                              <div className="document-image-container">
                                <img src={rider.moto_pic_url} alt="Motorcycle" />
                                <button
                                  type="button"
                                  className="document-expand-button"
                                  onClick={() => openDocumentModal(rider.moto_pic_url, 'Motorcycle')}
                                  aria-label="View Motorcycle in full size"
                                >
                                  <FaExpand />
                                  <span className="sr-only">View full size</span>
                                </button>
                              </div>
                            </div>
                          )}
                          {rider.vehicle_registration_pic_url && (
                            <div className="document-item">
                              <p>Vehicle Registration</p>
                              <div className="document-image-container">
                                <img src={rider.vehicle_registration_pic_url} alt="Vehicle Registration" />
                                <button
                                  type="button"
                                  className="document-expand-button"
                                  onClick={() => openDocumentModal(rider.vehicle_registration_pic_url, 'Vehicle Registration')}
                                  aria-label="View Vehicle Registration in full size"
                                >
                                  <FaExpand />
                                  <span className="sr-only">View full size</span>
                                </button>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="rider-actions">
                        <button
                          type="button"
                          onClick={() => handleApproveRider(rider.id)}
                          className="approve-button"
                        >
                          <FaCheck />
                          <span>Approve</span>
                        </button>
                        <button
                          type="button"
                          onClick={() => handleRejectRider(rider.id)}
                          className="reject-button"
                        >
                          <FaTimes />
                          <span>Reject</span>
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
          )}

          {activeTab === 'customers' && (
          <div className="customers-tab">
            <h2 className="dashboard-section-title">
              <FaUsers className="dashboard-section-icon" />
              Customer Management
            </h2>

            {customerStatistics && (
              <div className="customer-statistics-container">
                <div className="statistics-section customer-statistics">
                  <div className="statistics-header">
                    <h3 className="statistics-title">
                      <FaUsers className="statistics-icon" />
                      Customer Statistics Overview
                    </h3>
                  </div>
                  <div className="customer-statistics-content">
                    <div className="customer-stats-grid">
                      <div className="customer-stat-card">
                        <div className="customer-stat-icon">
                          <FaUsers />
                        </div>
                        <div className="customer-stat-info">
                          <h4>Total Customers</h4>
                          <p className="customer-stat-value">{customerStatistics.total_customers || 0}</p>
                        </div>
                      </div>

                      <div className="customer-stat-card">
                        <div className="customer-stat-icon">
                          <FaUserPlus />
                        </div>
                        <div className="customer-stat-info">
                          <h4>New Customers This Month</h4>
                          <p className="customer-stat-value">{customerStatistics.new_customers_this_month || 0}</p>
                        </div>
                      </div>
                    </div>

                    <div className="customer-statistics-sections">
                      {customerStatistics.monthly_signups && customerStatistics
                        .monthly_signups.length > 0 && (
                        <div className="monthly-signups-section">
                          <h4 className="statistics-section-title">Monthly Signups</h4>
                          <div className="admin-table-container">
                            <table className="admin-table">
                              <thead>
                                <tr>
                                  <th>Month</th>
                                  <th>New Signups</th>
                                </tr>
                              </thead>
                              <tbody>
                                {customerStatistics.monthly_signups.map((monthData) => (
                                  <tr key={`month-signup-${monthData.month}`}>
                                    <td>{monthData.month}</td>
                                    <td>{monthData.signup_count}</td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      )}

                      {customerStatistics.top_customers_by_orders && customerStatistics
                        .top_customers_by_orders.length > 0 && (
                        <div className="top-customers-section">
                          <h4 className="statistics-section-title">Top Customers by Orders</h4>
                          <div className="admin-table-container">
                            <table className="admin-table">
                              <thead>
                                <tr>
                                  <th>ID</th>
                                  <th>Customer</th>
                                  <th>Orders</th>
                                </tr>
                              </thead>
                              <tbody>
                                {customerStatistics.top_customers_by_orders
                                  .map((customer) => (
                                    <tr key={`order-${customer.id}`}>
                                      <td>{customer.id}</td>
                                      <td>{customer.name}</td>
                                      <td>{customer.order_count}</td>
                                    </tr>
                                  ))}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      )}

                      {customerStatistics.top_customers_by_spending && customerStatistics
                        .top_customers_by_spending.length > 0 && (
                        <div className="top-customers-section">
                          <h4 className="statistics-section-title">Top Customers by Spending</h4>
                          <div className="admin-table-container">
                            <table className="admin-table">
                              <thead>
                                <tr>
                                  <th>ID</th>
                                  <th>Customer</th>
                                  <th>Total Spent</th>
                                </tr>
                              </thead>
                              <tbody>
                                {customerStatistics.top_customers_by_spending
                                  .map((customer) => (
                                    <tr key={`spend-${customer.id}`}>
                                      <td>{customer.id}</td>
                                      <td>{customer.name}</td>
                                      <td className="price-cell">
                                        <span className="currency">GH₵</span>
                                        <span className="amount">{customer.total_spent}</span>
                                      </td>
                                    </tr>
                                  ))}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {customers.length === 0 ? (
              <div className="empty-state">
                <FaUsers className="empty-icon" />
                <p>No customers found in the system</p>
              </div>
            ) : (
              <div className="admin-table-container">
                <div className="table-header-actions">
                  <div className="table-search">
                    <input
                      type="text"
                      placeholder="Search customers..."
                      className="search-input"
                    />
                  </div>
                </div>
                <table className="admin-table">
                  <thead>
                    <tr>
                      <th>ID</th>
                      <th>Name</th>
                      <th>Email</th>
                      <th>Phone</th>
                      <th>Address</th>
                      <th>Joined</th>
                    </tr>
                  </thead>
                  <tbody>
                    {customers.map((customer) => (
                      <tr key={customer.id}>
                        <td>{customer.id}</td>
                        <td>
                          <div className="customer-name-cell">
                            <div className="customer-avatar">{customer.name.charAt(0).toUpperCase()}</div>
                            <span>{customer.name}</span>
                          </div>
                        </td>
                        <td>{customer.email}</td>
                        <td>{customer.phone}</td>
                        <td>{customer.address}</td>
                        <td>{new Date(customer.created_at).toLocaleDateString()}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
          )}

          {activeTab === 'categories' && (
          <div className="categories-tab">
            <h2 className="dashboard-section-title">
              <FaTags className="dashboard-section-icon" />
              Food Categories
            </h2>

            {categories.length === 0 ? (
              <div className="empty-state">
                <FaTags className="empty-icon" />
                <p>No food categories found in the system</p>
              </div>
            ) : (
              <div className="admin-table-container">
                <div className="table-header-actions">
                  <button type="button" className="add-button">
                    <FaPlus />
                    <span>Add Category</span>
                  </button>
                </div>
                <table className="admin-table">
                  <thead>
                    <tr>
                      <th>ID</th>
                      <th>Name</th>
                      <th>Description</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {categories.map((category) => (
                      <tr key={category.id}>
                        <td>{category.id}</td>
                        <td>
                          <div className="category-name">
                            <FaTag className="category-icon" />
                            {category.name}
                          </div>
                        </td>
                        <td>{category.description}</td>
                        <td>
                          <div className="table-actions">
                            <button
                              type="button"
                              className="edit-button"
                              aria-label={`Edit ${category.name} category`}
                            >
                              <FaEdit />
                              <span className="sr-only">Edit</span>
                            </button>
                            <button
                              type="button"
                              className="delete-button"
                              aria-label={`Delete ${category.name} category`}
                            >
                              <FaTrash />
                              <span className="sr-only">Delete</span>
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
          )}

          {activeTab === 'complaints' && (
          <div className="complaints-tab">
            <h2 className="dashboard-section-title">
              <FaCommentAlt className="dashboard-section-icon" />
              Customer Complaints
              <span className="count">
                (
                {complaintStatistics?.total_complaints || 0}
                )
              </span>
            </h2>

            {complaintStatistics && (
              <div className="dashboard-stats">
                <div className="stat-card pending">
                  <div className="stat-icon">
                    <FaSpinner />
                  </div>
                  <div className="stat-content">
                    <h3>Pending</h3>
                    <p className="stat-value">{complaintStatistics?.complaints_by_status?.pending || 0}</p>
                  </div>
                </div>
                <div className="stat-card in-progress">
                  <div className="stat-icon">
                    <FaInfoCircle />
                  </div>
                  <div className="stat-content">
                    <h3>Under Review</h3>
                    <p className="stat-value">{complaintStatistics?.complaints_by_status?.under_review || 0}</p>
                  </div>
                </div>
                <div className="stat-card resolved">
                  <div className="stat-icon">
                    <FaCheck />
                  </div>
                  <div className="stat-content">
                    <h3>Resolved</h3>
                    <p className="stat-value">{complaintStatistics?.complaints_by_status?.resolved || 0}</p>
                  </div>
                </div>
                <div className="stat-card rejected">
                  <div className="stat-icon">
                    <FaTimesCircle />
                  </div>
                  <div className="stat-content">
                    <h3>Rejected</h3>
                    <p className="stat-value">{complaintStatistics?.complaints_by_status?.rejected || 0}</p>
                  </div>
                </div>
              </div>
            )}

            {complaints && complaints.length === 0 ? (
              <div className="empty-state">
                <FaCommentAlt className="empty-icon" />
                <p>No complaints found in the system</p>
              </div>
            ) : (
              <div className="admin-table-container">
                <table className="admin-table">
                  <thead>
                    <tr>
                      <th>ID</th>
                      <th>Customer</th>
                      <th>Type</th>
                      <th>Description</th>
                      <th>Status</th>
                      <th>Date</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {complaints && complaints.map((complaint) => (
                      <tr key={complaint.id}>
                        <td>{complaint.id}</td>
                        <td>
                          <div className="customer-name-cell">
                            <div className="customer-avatar">
                              {complaint.name ? complaint.name.charAt(0).toUpperCase() : 'U'}
                            </div>
                            <span>{complaint.name || 'Unknown'}</span>
                          </div>
                        </td>
                        <td>{complaint.category}</td>
                        <td>
                          <div className="description-cell">
                            {complaint.complaint && (
                              complaint.complaint.length > 100
                                ? `${complaint.complaint.substring(0, 100)}...`
                                : complaint.complaint
                            )}
                            {complaint.image_url && (
                              <button
                                type="button"
                                className="view-image-button"
                                onClick={() => openDocumentModal(complaint.image_url, 'Complaint Image')}
                              >
                                <FaEye />
                                <span>View Image</span>
                              </button>
                            )}
                          </div>
                        </td>
                        <td>
                          <div className={`status-badge ${complaint.status}`}>
                            {complaint.status.replace('_', ' ')}
                          </div>
                        </td>
                        <td>{new Date(complaint.created_at).toLocaleDateString()}</td>
                        <td>
                          <div className="table-actions">
                            {complaint.status === 'pending' && (
                              <button
                                type="button"
                                className="action-button in-progress-button"
                                onClick={() => handleUpdateComplaintStatus(complaint.id, 'under_review')}
                              >
                                <FaInfoCircle />
                                <span>Mark Under Review</span>
                              </button>
                            )}
                            {(complaint.status === 'pending' || complaint.status === 'under_review') && (
                              <>
                                <button
                                  type="button"
                                  className="action-button resolve-button"
                                  onClick={() => handleUpdateComplaintStatus(complaint.id, 'resolved')}
                                >
                                  <FaCheck />
                                  <span>Mark Resolved</span>
                                </button>
                                <button
                                  type="button"
                                  className="action-button reject-button"
                                  onClick={() => handleUpdateComplaintStatus(complaint.id, 'rejected')}
                                >
                                  <FaTimesCircle />
                                  <span>Reject</span>
                                </button>
                              </>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
          )}

          {activeTab === 'suggestions' && (
          <div className="suggestions-tab">
            <h2 className="dashboard-section-title">
              <FaLightbulb className="dashboard-section-icon" />
              Customer Suggestions
              <span className="count">
                (
                {suggestionStatistics?.total_suggestions || 0}
                )
              </span>
            </h2>

            {suggestionStatistics && (
              <div className="dashboard-stats">
                <div className="stat-card pending">
                  <div className="stat-icon">
                    <FaSpinner />
                  </div>
                  <div className="stat-content">
                    <h3>Pending</h3>
                    <p className="stat-value">{suggestionStatistics?.suggestions_by_status?.pending || 0}</p>
                  </div>
                </div>
                <div className="stat-card in-progress">
                  <div className="stat-icon">
                    <FaInfoCircle />
                  </div>
                  <div className="stat-content">
                    <h3>Under Review</h3>
                    <p className="stat-value">{suggestionStatistics?.suggestions_by_status?.under_review || 0}</p>
                  </div>
                </div>
                <div className="stat-card implemented">
                  <div className="stat-icon">
                    <FaClipboardCheck />
                  </div>
                  <div className="stat-content">
                    <h3>Implemented</h3>
                    <p className="stat-value">{suggestionStatistics?.suggestions_by_status?.implemented || 0}</p>
                  </div>
                </div>
                <div className="stat-card rejected">
                  <div className="stat-icon">
                    <FaTimesCircle />
                  </div>
                  <div className="stat-content">
                    <h3>Rejected</h3>
                    <p className="stat-value">{suggestionStatistics?.suggestions_by_status?.rejected || 0}</p>
                  </div>
                </div>
              </div>
            )}

            {suggestions && suggestions.length === 0 ? (
              <div className="empty-state">
                <FaLightbulb className="empty-icon" />
                <p>No suggestions found in the system</p>
              </div>
            ) : (
              <div className="admin-table-container">
                <table className="admin-table">
                  <thead>
                    <tr>
                      <th>ID</th>
                      <th>Customer</th>
                      <th>Category</th>
                      <th>Suggestion</th>
                      <th>Rating</th>
                      <th>Status</th>
                      <th>Date</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {suggestions && suggestions.map((suggestion) => (
                      <tr key={suggestion.id}>
                        <td>{suggestion.id}</td>
                        <td>
                          <div className="customer-name-cell">
                            <div className="customer-avatar">
                              {suggestion.name ? suggestion.name.charAt(0).toUpperCase() : 'U'}
                            </div>
                            <span>{suggestion.name || 'Unknown'}</span>
                          </div>
                        </td>
                        <td>{suggestion.category}</td>
                        <td>
                          <div className="description-cell">
                            {suggestion.suggestion && (
                              suggestion.suggestion.length > 100
                                ? `${suggestion.suggestion.substring(0, 100)}...`
                                : suggestion.suggestion
                            )}
                          </div>
                        </td>
                        <td>
                          <div className="rating-display">
                            {suggestion.rating >= 1 && <span className="star filled">★</span>}
                            {suggestion.rating >= 2 && <span className="star filled">★</span>}
                            {suggestion.rating >= 3 && <span className="star filled">★</span>}
                            {suggestion.rating >= 4 && <span className="star filled">★</span>}
                            {suggestion.rating >= 5 && <span className="star filled">★</span>}
                            {suggestion.rating < 5 && <span className="star">★</span>}
                            {suggestion.rating < 4 && <span className="star">★</span>}
                            {suggestion.rating < 3 && <span className="star">★</span>}
                            {suggestion.rating < 2 && <span className="star">★</span>}
                            {suggestion.rating < 1 && <span className="star">★</span>}
                          </div>
                        </td>
                        <td>
                          <div className={`status-badge ${suggestion.status}`}>
                            {suggestion.status.replace('_', ' ')}
                          </div>
                        </td>
                        <td>{new Date(suggestion.created_at).toLocaleDateString()}</td>
                        <td>
                          <div className="table-actions">
                            {suggestion.status === 'pending' && (
                              <button
                                type="button"
                                className="action-button review-button"
                                onClick={() => handleUpdateSuggestionStatus(suggestion.id, 'under_review')}
                              >
                                <FaInfoCircle />
                                <span>Mark Under Review</span>
                              </button>
                            )}
                            {(suggestion.status === 'pending' || suggestion.status === 'under_review') && (
                              <>
                                <button
                                  type="button"
                                  className="action-button implement-button"
                                  onClick={() => handleUpdateSuggestionStatus(suggestion.id, 'implemented')}
                                >
                                  <FaClipboardCheck />
                                  <span>Mark Implemented</span>
                                </button>
                                <button
                                  type="button"
                                  className="action-button reject-button"
                                  onClick={() => handleUpdateSuggestionStatus(suggestion.id, 'rejected')}
                                >
                                  <FaTimesCircle />
                                  <span>Reject</span>
                                </button>
                              </>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
