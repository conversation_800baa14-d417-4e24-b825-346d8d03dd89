import { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { toast } from 'react-toastify';
import {
  User,
  Phone,
  Mail,
  MapPin,
  Calendar,
  Truck,
  CreditCard,
  FileText,
  Upload,
  Shield,
} from 'react-feather';
import { fetchUserData, updateUserData } from '../../redux/slice/userSlice';
import Loader from '../helper-functions/Loader';
import profile from '../../assets/profile.png';
import PushNotificationSettings from '../notifications/PushNotificationSettings';

const Profile = () => {
  const dispatch = useDispatch();
  const { user, role } = useSelector((state) => state.auth);
  const { userData, loading, error } = useSelector((state) => state.user);

  const [formData, setFormData] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch user data when component mounts.
  useEffect(() => {
    if (user && role) {
      dispatch(fetchUserData({ role, userId: user.id }));
    }
  }, [dispatch, user, role]);

  // Update form data when userData changes.
  useEffect(() => {
    if (userData) {
      // Map account_name to mobile_money_name for UI display
      setFormData({
        ...userData,
        mobile_money_name: userData.account_name || '',
      });
    }
  }, [userData]);

  const handleChange = (e) => {
    const { name, value, files } = e.target;

    if (files) {
      setFormData((prev) => ({ ...prev, [name]: files[0] }));
    } else if (name === 'mobile_money_provider') {
      // Special handling for mobile_money_provider to ensure it's in the correct format
      // Make sure it's lowercase to match backend expectations
      setFormData((prev) => ({ ...prev, [name]: value.toLowerCase() }));
    } else if (name === 'operation_time' || name === 'closing_time') {
      // For time inputs, we need to convert the HH:MM format to a full ISO string
      // that the backend expects
      const [hours, minutes] = value.split(':');

      // Create a date object with the time (using a fixed date)
      const date = new Date();
      date.setHours(hours);
      date.setMinutes(minutes);
      date.setSeconds(0);

      // Format as ISO string
      setFormData((prev) => ({ ...prev, [name]: date.toISOString() }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
  };

  // Validate mobile money provider
  const validateMobileMoneyProvider = (provider) => {
    if (!provider) return true; // Empty is valid (will use backend default)

    // Valid providers according to backend
    const validProviders = ['mtn', 'vodafone', 'airteltigo'];
    return validProviders.includes(provider.toLowerCase());
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true); // Mark submission in progress

    // Validate mobile money provider if present
    const provider = formData.mobile_money_provider;
    if (provider && !validateMobileMoneyProvider(provider)) {
      toast.error('Invalid mobile money provider. Please select a valid option.');
      setIsSubmitting(false);
      return;
    }

    const dataToSubmit = new FormData();
    Object.keys(formData).forEach((key) => {
      // Skip empty values for mobile_money_provider to use backend defaults
      if (key === 'mobile_money_provider' && !formData[key]) {
        return;
      }

      // Map mobile_money_name to account_name for backend
      if (key === 'mobile_money_name') {
        dataToSubmit.append(`${role}[account_name]`, formData[key]);
      } else if (key !== 'account_name') { // Skip account_name as we're using mobile_money_name instead
        dataToSubmit.append(`${role}[${key}]`, formData[key]);
      }
    });

    try {
      const result = await dispatch(updateUserData({
        role,
        userId: user.id,
        userData: dataToSubmit,
      })).unwrap();

      toast.success('Profile updated successfully!');
      setFormData(result); // Update formData with the latest data from the server
    } catch (err) {
      toast.error(`Failed to update profile: ${Array.isArray(err) ? err.join(', ') : err}`);
    } finally {
      setIsSubmitting(false); // Reset submission state
    }
  };

  if (loading) return <Loader />;
  if (error && !userData) {
    return (
      <p>
        Error:
        {error}
      </p>
    );
  }

  // Define fields for each role based on your schema and controllers.
  const roleFields = {
    customer: ['name', 'phone', 'username', 'address', 'email', 'avatar'],
    vendor: [
      'name', 'phone', 'username', 'address', 'operation_time', 'closing_time',
      'digital_address', 'ghana_card_number', 'front_ghana_card', 'back_ghana_card',
      'health_certificate_pic', 'food_certificate_pic', 'operation_license_pic',
      // Mobile Money fields
      'mobile_money_number', 'mobile_money_provider', 'mobile_money_name',
    ],
    rider: [
      'name', 'phone', 'username', 'address', 'vehicle_details', 'within_dunkwa_price', 'outside_dunkwa_price',
      'rider_status', 'dob', 'drivers_license_number', 'ghana_card_number',
      'vehicle_registration_number', 'avatar', 'front_drivers_license', 'back_drivers_license',
      'front_ghana_card', 'back_ghana_card', 'moto_pic', 'vehicle_registration_pic',
      // Mobile Money fields
      'mobile_money_number', 'mobile_money_provider', 'mobile_money_name',
    ],
  };

  const fields = roleFields[role] || [];

  const getInputType = (field) => {
    switch (field) {
      case 'email':
        return 'email';
      case 'dob':
        return 'date';
      case 'operation_time':
      case 'closing_time':
        return 'time';
      case 'mobile_money_provider':
        return 'select';
      default:
        return 'text';
    }
  };

  // The backend maps these values as follows:
  // "mtn" -> "mtn"
  // "vodafone" -> "vod"
  // "airteltigo", "airtel", "tigo" -> "tgo"
  const getMobileMoneyOptions = () => (
    <>
      <option value="">Select Provider</option>
      <option value="mtn">MTN Mobile Money</option>
      <option value="vodafone">Vodafone Cash</option>
      <option value="airteltigo">AirtelTigo Money</option>
    </>
  );

  // Helper function to render the appropriate input field based on field type
  const renderInputField = (field) => {
    // Check if it's a file input field
    if (field.includes('pic')
        || field.includes('avatar')
        || field.includes('license')
        || field.includes('card')) {
      return <input type="file" name={field} onChange={handleChange} />;
    }

    // Check if it's a select field
    if (getInputType(field) === 'select') {
      return (
        <select
          name={field}
          value={formData[field] || ''}
          onChange={handleChange}
          className="form-input"
        >
          {field === 'mobile_money_provider' && getMobileMoneyOptions()}
        </select>
      );
    }

    // Special handling for time inputs
    if (field === 'operation_time' || field === 'closing_time') {
      // Extract time from ISO string or use formatted time if available
      let timeValue = '';

      if (formData[field]) {
        // If it's a full ISO date string, extract just the time part
        if (formData[field].includes('T')) {
          const date = new Date(formData[field]);
          // Format as HH:MM in 24-hour format for time input
          timeValue = date.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false,
            timeZone: 'Africa/Accra', // Ghana timezone
          });
        } else {
          // If it's already in HH:MM format, use it directly
          timeValue = formData[field];
        }
      }

      return (
        <input
          type="time"
          name={field}
          value={timeValue}
          onChange={handleChange}
        />
      );
    }

    // Special handling for mobile_money_name field
    if (field === 'mobile_money_name') {
      // Use account_name as a fallback if mobile_money_name is not available
      const value = formData[field] || formData.account_name || '';
      return (
        <input
          type="text"
          name={field}
          value={value}
          onChange={handleChange}
          placeholder="Enter Mobile Money Account Name"
        />
      );
    }

    // Default to regular input field
    return (
      <input
        type={getInputType(field)}
        name={field}
        value={formData[field] || ''}
        onChange={handleChange}
        step={field.includes('price') ? '0.01' : undefined}
      />
    );
  };

  // Helper function to get the appropriate icon for a field
  const getFieldIcon = (field) => {
    switch (field) {
      case 'name':
        return <User size={16} className="profile-field-icon" />;
      case 'phone':
      case 'mobile_money_number':
        return <Phone size={16} className="profile-field-icon" />;
      case 'email':
        return <Mail size={16} className="profile-field-icon" />;
      case 'address':
      case 'digital_address':
        return <MapPin size={16} className="profile-field-icon" />;
      case 'dob':
      case 'operation_time':
      case 'closing_time':
        return <Calendar size={16} className="profile-field-icon" />;
      case 'vehicle_details':
      case 'within_dunkwa_price':
      case 'outside_dunkwa_price':
        return <Truck size={16} className="profile-field-icon" />;
      case 'mobile_money_provider':
      case 'mobile_money_name':
        return <CreditCard size={16} className="profile-field-icon" />;
      case 'ghana_card_number':
      case 'drivers_license_number':
      case 'vehicle_registration_number':
        return <FileText size={16} className="profile-field-icon" />;
      case 'rider_status':
        return <Shield size={16} className="profile-field-icon" />;
      default:
        if (field.includes('pic') || field.includes('avatar') || field.includes('license') || field.includes('card')) {
          return <Upload size={16} className="profile-field-icon" />;
        }
        return <FileText size={16} className="profile-field-icon" />;
    }
  };

  // Group fields by category
  const getFieldGroups = () => {
    const basicFields = ['name', 'username', 'email', 'phone', 'address', 'digital_address'];
    const documentFields = [
      'ghana_card_number', 'front_ghana_card', 'back_ghana_card',
      'drivers_license_number', 'front_drivers_license', 'back_drivers_license',
      'health_certificate_pic', 'food_certificate_pic', 'operation_license_pic',
      'vehicle_registration_number', 'vehicle_registration_pic', 'moto_pic',
    ];
    const businessFields = ['operation_time', 'closing_time'];
    const riderFields = ['vehicle_details', 'within_dunkwa_price', 'outside_dunkwa_price', 'rider_status', 'dob'];
    const paymentFields = ['mobile_money_number', 'mobile_money_provider', 'mobile_money_name'];

    const groups = {
      basic: fields.filter((field) => basicFields.includes(field)),
      documents: fields.filter((field) => documentFields.includes(field)),
      business: fields.filter((field) => businessFields.includes(field)),
      rider: fields.filter((field) => riderFields.includes(field)),
      payment: fields.filter((field) => paymentFields.includes(field)),
    };

    // Only return groups that have fields
    return Object.fromEntries(Object.entries(groups)
      .filter(([, groupFields]) => groupFields.length > 0));
  };

  return (
    <div className="profile-container">
      <div className="profile-header">
        <h2 className="profile-title">My Profile</h2>
        <p className="profile-role">{role}</p>
      </div>

      {userData && (
        <div className="profile-content">
          <div className="avatar-container">
            <img
              className="avatar-image"
              src={userData.avatar_url || profile}
              alt="Avatar"
            />
            {fields.includes('avatar') && (
              <button
                type="button"
                className="avatar-upload"
                onClick={() => document.getElementById('avatar-input').click()}
                aria-label="Change profile photo"
              >
                <span>Change Photo</span>
                <input
                  id="avatar-input"
                  type="file"
                  name="avatar"
                  onChange={handleChange}
                  style={{ display: 'none' }}
                  aria-label="Upload profile photo"
                />
              </button>
            )}
          </div>

          <form className="profile-form" onSubmit={handleSubmit}>
            {Object.entries(getFieldGroups()).map(([groupName, groupFields]) => (
              <div key={groupName} className="profile-section">
                <h3 className="profile-section-title">
                  {groupName.charAt(0).toUpperCase() + groupName.slice(1)}
                  {' Information'}
                </h3>

                {groupName === 'payment' && (
                  <div className="mobile-money-container">
                    <h4 className="mobile-money-title">Mobile Money Details</h4>
                    <p>These details will be used for payouts when orders are completed.</p>
                  </div>
                )}

                <div className="profile-fields-grid">
                  {groupFields.map((field) => (
                    <div key={field} className="profile-field">
                      <label className="profile-field-label" htmlFor={`field-${field}`}>
                        {getFieldIcon(field)}
                        {field.replace(/_/g, ' ').toUpperCase()}
                      </label>
                      <div id={`field-${field}`}>
                        {renderInputField(field)}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}

            <button
              type="submit"
              className="profile-submit-btn"
              disabled={loading || isSubmitting}
            >
              {isSubmitting ? 'Updating...' : 'Update Profile'}
            </button>
          </form>

          {/* Push Notification Settings */}
          <div className="profile-section">
            <h3 className="profile-section-title">Notification Settings</h3>
            <PushNotificationSettings />
          </div>
        </div>
      )}
    </div>
  );
};

export default Profile;
