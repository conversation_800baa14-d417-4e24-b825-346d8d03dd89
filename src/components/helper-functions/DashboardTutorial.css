/* Tutorial Overlay */
.tutorial-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 9999;
  backdrop-filter: blur(2px);
}

/* Tutorial Modal */
.tutorial-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  z-index: 10000;
  animation: tutorialSlideIn 0.3s ease-out;
}

@keyframes tutorialSlideIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }

  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* Tutorial Header */
.tutorial-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 1.5rem 1rem;
  border-bottom: 1px solid #e0e0e0;
  background: linear-gradient(135deg, #f8b400 0%, #e5a700 100%);
  color: white;
  flex-shrink: 0;
}

.tutorial-progress {
  flex: 1;
  margin-right: 1rem;
}

.tutorial-step-counter {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 0.5rem;
}

.tutorial-progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
}

.tutorial-progress-fill {
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.tutorial-close-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tutorial-close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* Tutorial Content */
.tutorial-content {
  padding: 1.5rem;
  text-align: center;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.tutorial-icon {
  margin-bottom: 1rem;
  display: flex;
  justify-content: center;
}

.tutorial-icon svg {
  width: 48px;
  height: 48px;
  color: #f8b400;
}

.tutorial-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.tutorial-description {
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.tutorial-details {
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
  text-align: left;
  font-size: 0.875rem;
  color: #333;
  line-height: 1.5;
}

/* Tutorial Footer */
.tutorial-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem 1.5rem;
  border-top: 1px solid #e0e0e0;
  background: #f8f9fa;
  flex-shrink: 0;
}

.tutorial-skip-btn {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tutorial-skip-btn:hover {
  background: #e0e0e0;
  color: #333;
}

.tutorial-navigation {
  display: flex;
  gap: 0.75rem;
}

.tutorial-nav-btn,
.tutorial-next-btn {
  padding: 0.75rem 1.25rem;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tutorial-nav-btn {
  background: #f3f4f6;
  color: #666;
}

.tutorial-nav-btn:hover:not(.disabled) {
  background: #e0e0e0;
  color: #333;
}

.tutorial-nav-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.tutorial-next-btn {
  background: linear-gradient(135deg, #f8b400 0%, #e5a700 100%);
  color: white;
}

.tutorial-next-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(248, 180, 0, 0.4);
}

/* Highlight effect for target elements */
.tutorial-highlight {
  position: relative;
  z-index: 9997;
  animation: tutorialPulse 2s infinite;
  border-radius: 8px;
}

@keyframes tutorialPulse {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(248, 180, 0, 0.7);
  }

  50% {
    box-shadow: 0 0 0 8px rgba(248, 180, 0, 0.3);
  }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .tutorial-modal {
    width: 95%;
    max-height: 85vh;
    display: flex;
    flex-direction: column;
  }

  .tutorial-header {
    padding: 1rem 1rem 0.75rem;
    flex-shrink: 0;
  }

  .tutorial-content {
    padding: 1rem;
    flex: 1;
    overflow-y: auto;
    min-height: 0;
  }

  .tutorial-footer {
    padding: 0.75rem 1rem 1rem;
    flex-direction: column;
    gap: 1rem;
    flex-shrink: 0;
  }

  .tutorial-navigation {
    width: 100%;
    justify-content: space-between;
  }

  .tutorial-nav-btn,
  .tutorial-next-btn {
    flex: 1;
    justify-content: center;
  }

  .tutorial-skip-btn {
    align-self: flex-start;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .tutorial-modal {
    background: #1f2937;
    color: white;
  }

  .tutorial-header {
    border-bottom-color: #374151;
  }

  .tutorial-title {
    color: white;
  }

  .tutorial-description {
    color: #d1d5db;
  }

  .tutorial-details {
    background: #374151;
    border-color: #4b5563;
    color: #d1d5db;
  }

  .tutorial-footer {
    background: #374151;
    border-top-color: #4b5563;
  }

  .tutorial-close-btn:hover {
    background: #4b5563;
    color: white;
  }

  .tutorial-skip-btn:hover {
    background: #4b5563;
    color: white;
  }

  .tutorial-nav-btn {
    background: #4b5563;
    color: #d1d5db;
  }

  .tutorial-nav-btn:hover:not(.disabled) {
    background: #6b7280;
    color: white;
  }
}
