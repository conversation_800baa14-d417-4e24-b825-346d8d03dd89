.auto-refresh-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  margin: 1rem 0;
}

.auto-refresh-button {
  position: relative;
  bottom: 3.5rem;
  left: 9rem;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  border: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.auto-refresh-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.auto-refresh-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.auto-refresh-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.auto-refresh-button.refreshing {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.refresh-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.refresh-icon {
  z-index: 2;
  transition: transform 0.3s ease;
}

.refresh-icon.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.progress-ring {
  position: absolute;
  bottom: 0.99px;
  right: 2px;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.progress-ring-background {
  stroke: rgba(255, 255, 255, 0.3);
}

.progress-ring-progress {
  stroke: rgba(255, 255, 255, 0.8);
  stroke-linecap: round;
  transition: stroke-dashoffset 0.3s ease;
}

.dash {
  position: relative;
  top: 3rem;
}

.custom-dash {
  position: relative;
  right: 19rem;
  top: 2rem;
}

.refresh-status {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 0.75rem;
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
  backdrop-filter: blur(10px);
}

.status-text {
  opacity: 0.9;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .auto-refresh-container {
    margin: 0.5rem 0;
  }

  .refresh-status {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .refresh-status {
    background: rgba(255, 255, 255, 0.1);
    color: white;
  }
}
